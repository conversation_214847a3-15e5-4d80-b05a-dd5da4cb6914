"""
性能分析和报告模块
生成详细的回测报告，包括收益曲线、交易统计、风险指标等
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple
import logging
from datetime import datetime

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def calculate_performance_metrics(self, equity_curve: List[float], 
                                    trades: List, timestamps: List[pd.Timestamp],
                                    initial_capital: float) -> Dict:
        """计算详细的性能指标"""
        if not equity_curve or len(equity_curve) < 2:
            return {}
        
        equity_series = pd.Series(equity_curve, index=timestamps[:len(equity_curve)])
        returns = equity_series.pct_change().dropna()
        
        # 基础指标
        total_return = (equity_curve[-1] - initial_capital) / initial_capital
        annual_return = self._annualize_return(returns)
        volatility = returns.std() * np.sqrt(365 * 24)  # 年化波动率
        
        # 风险指标
        max_drawdown = self._calculate_max_drawdown(equity_series)
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        
        # 风险调整收益指标
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 交易统计
        trade_stats = self._calculate_trade_statistics(trades)
        
        # 月度收益
        monthly_returns = self._calculate_monthly_returns(equity_series)
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'var_95': var_95,
            'var_99': var_99,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'trade_statistics': trade_stats,
            'monthly_returns': monthly_returns
        }
    
    def _annualize_return(self, returns: pd.Series) -> float:
        """年化收益率"""
        if len(returns) == 0:
            return 0
        return returns.mean() * 365 * 24  # 假设小时数据
    
    def _calculate_max_drawdown(self, equity_series: pd.Series) -> float:
        """计算最大回撤"""
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        return drawdown.min()
    
    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """计算索提诺比率"""
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0:
            return float('inf')
        
        downside_std = negative_returns.std()
        if downside_std == 0:
            return float('inf')
        
        return returns.mean() / downside_std * np.sqrt(365 * 24)
    
    def _calculate_trade_statistics(self, trades: List) -> Dict:
        """计算交易统计"""
        if not trades:
            return {}
        
        profits = [trade.pnl for trade in trades if trade.pnl > 0]
        losses = [trade.pnl for trade in trades if trade.pnl < 0]
        
        win_rate = len(profits) / len(trades) if trades else 0
        profit_factor = sum(profits) / abs(sum(losses)) if losses else float('inf')
        
        avg_profit = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # 交易持续时间
        durations = [trade.duration.total_seconds() / 3600 for trade in trades]  # 小时
        avg_duration = np.mean(durations) if durations else 0
        
        # 连续盈亏
        consecutive_wins, consecutive_losses = self._calculate_consecutive_trades(trades)
        
        return {
            'total_trades': len(trades),
            'winning_trades': len(profits),
            'losing_trades': len(losses),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'avg_duration_hours': avg_duration,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'largest_profit': max(profits) if profits else 0,
            'largest_loss': min(losses) if losses else 0
        }
    
    def _calculate_consecutive_trades(self, trades: List) -> Tuple[int, int]:
        """计算最大连续盈亏次数"""
        if not trades:
            return 0, 0
        
        max_wins = max_losses = 0
        current_wins = current_losses = 0
        
        for trade in trades:
            if trade.pnl > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
        
        return max_wins, max_losses
    
    def _calculate_monthly_returns(self, equity_series: pd.Series) -> pd.Series:
        """计算月度收益率"""
        monthly_equity = equity_series.resample('M').last()
        monthly_returns = monthly_equity.pct_change().dropna()
        return monthly_returns
    
    def plot_equity_curve(self, equity_curve: List[float], 
                         timestamps: List[pd.Timestamp],
                         initial_capital: float,
                         save_path: str = None) -> go.Figure:
        """绘制资金曲线"""
        equity_series = pd.Series(equity_curve, index=timestamps[:len(equity_curve)])
        
        fig = go.Figure()
        
        # 资金曲线
        fig.add_trace(go.Scatter(
            x=equity_series.index,
            y=equity_series.values,
            mode='lines',
            name='资金曲线',
            line=dict(color='blue', width=2)
        ))
        
        # 基准线
        fig.add_hline(y=initial_capital, line_dash="dash", 
                     line_color="red", annotation_text="初始资金")
        
        # 计算回撤
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak * 100
        
        # 添加回撤区域
        fig.add_trace(go.Scatter(
            x=equity_series.index,
            y=drawdown,
            mode='lines',
            name='回撤 (%)',
            line=dict(color='red', width=1),
            yaxis='y2'
        ))
        
        fig.update_layout(
            title='马丁格尔策略资金曲线',
            xaxis_title='时间',
            yaxis_title='资金 (USDT)',
            yaxis2=dict(
                title='回撤 (%)',
                overlaying='y',
                side='right'
            ),
            hovermode='x unified'
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def plot_trade_analysis(self, trades: List, save_path: str = None) -> go.Figure:
        """绘制交易分析图"""
        if not trades:
            return go.Figure()
        
        # 准备数据
        trade_data = pd.DataFrame([{
            'symbol': trade.symbol,
            'pnl': trade.pnl,
            'entry_time': trade.entry_time,
            'duration_hours': trade.duration.total_seconds() / 3600,
            'side': trade.side
        } for trade in trades])
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('盈亏分布', '交易时长分布', '累计盈亏', '按币种盈亏'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 盈亏分布直方图
        fig.add_trace(
            go.Histogram(x=trade_data['pnl'], nbinsx=30, name='盈亏分布'),
            row=1, col=1
        )
        
        # 交易时长分布
        fig.add_trace(
            go.Histogram(x=trade_data['duration_hours'], nbinsx=20, name='时长分布'),
            row=1, col=2
        )
        
        # 累计盈亏
        cumulative_pnl = trade_data['pnl'].cumsum()
        fig.add_trace(
            go.Scatter(x=trade_data['entry_time'], y=cumulative_pnl, 
                      mode='lines', name='累计盈亏'),
            row=2, col=1
        )
        
        # 按币种盈亏
        symbol_pnl = trade_data.groupby('symbol')['pnl'].sum().sort_values(ascending=True)
        fig.add_trace(
            go.Bar(x=symbol_pnl.values, y=symbol_pnl.index, 
                  orientation='h', name='币种盈亏'),
            row=2, col=2
        )
        
        fig.update_layout(
            title='交易分析报告',
            showlegend=False,
            height=800
        )
        
        if save_path:
            fig.write_html(save_path)
        
        return fig
    
    def generate_performance_report(self, performance_metrics: Dict, 
                                  strategy_params: Dict = None) -> str:
        """生成性能报告"""
        report = "=" * 60 + "\n"
        report += "马丁格尔量化交易策略回测报告\n"
        report += "=" * 60 + "\n\n"
        
        if strategy_params:
            report += "策略参数:\n"
            report += f"  初始资金: {strategy_params.get('initial_capital', 'N/A'):,} USDT\n"
            report += f"  杠杆倍数: {strategy_params.get('leverage', 'N/A')}x\n"
            report += f"  开仓比例: {strategy_params.get('position_size_pct', 'N/A'):.1%}\n"
            report += f"  最大加仓次数: {strategy_params.get('max_add_positions', 'N/A')}\n"
            report += f"  最大持仓币种: {strategy_params.get('max_positions', 'N/A')}\n\n"
        
        # 收益指标
        report += "收益指标:\n"
        report += f"  总收益率: {performance_metrics.get('total_return', 0):.2%}\n"
        report += f"  年化收益率: {performance_metrics.get('annual_return', 0):.2%}\n"
        report += f"  年化波动率: {performance_metrics.get('volatility', 0):.2%}\n\n"
        
        # 风险指标
        report += "风险指标:\n"
        report += f"  最大回撤: {performance_metrics.get('max_drawdown', 0):.2%}\n"
        report += f"  95% VaR: {performance_metrics.get('var_95', 0):.2%}\n"
        report += f"  99% VaR: {performance_metrics.get('var_99', 0):.2%}\n\n"
        
        # 风险调整收益
        report += "风险调整收益:\n"
        report += f"  夏普比率: {performance_metrics.get('sharpe_ratio', 0):.3f}\n"
        report += f"  索提诺比率: {performance_metrics.get('sortino_ratio', 0):.3f}\n"
        report += f"  卡尔玛比率: {performance_metrics.get('calmar_ratio', 0):.3f}\n\n"
        
        # 交易统计
        trade_stats = performance_metrics.get('trade_statistics', {})
        if trade_stats:
            report += "交易统计:\n"
            report += f"  总交易次数: {trade_stats.get('total_trades', 0)}\n"
            report += f"  盈利交易: {trade_stats.get('winning_trades', 0)}\n"
            report += f"  亏损交易: {trade_stats.get('losing_trades', 0)}\n"
            report += f"  胜率: {trade_stats.get('win_rate', 0):.2%}\n"
            report += f"  盈亏比: {trade_stats.get('profit_factor', 0):.2f}\n"
            report += f"  平均盈利: {trade_stats.get('avg_profit', 0):.2f} USDT\n"
            report += f"  平均亏损: {trade_stats.get('avg_loss', 0):.2f} USDT\n"
            report += f"  平均持仓时间: {trade_stats.get('avg_duration_hours', 0):.1f} 小时\n"
            report += f"  最大连续盈利: {trade_stats.get('max_consecutive_wins', 0)}\n"
            report += f"  最大连续亏损: {trade_stats.get('max_consecutive_losses', 0)}\n"
            report += f"  最大单笔盈利: {trade_stats.get('largest_profit', 0):.2f} USDT\n"
            report += f"  最大单笔亏损: {trade_stats.get('largest_loss', 0):.2f} USDT\n\n"
        
        # 月度收益
        monthly_returns = performance_metrics.get('monthly_returns')
        if monthly_returns is not None and not monthly_returns.empty:
            report += "月度收益率:\n"
            for date, ret in monthly_returns.items():
                report += f"  {date.strftime('%Y-%m')}: {ret:.2%}\n"
        
        report += "\n" + "=" * 60 + "\n"
        report += f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        return report
