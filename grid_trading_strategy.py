"""
网格交易策略 - 2024年最稳定盈利的策略之一
适合震荡市场，通过低买高卖赚取价差
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging
import json

@dataclass
class GridOrder:
    """网格订单"""
    price: float
    quantity: float
    side: str  # 'buy' or 'sell'
    status: str  # 'pending', 'filled', 'cancelled'
    order_id: str = None
    fill_time: datetime = None
    profit: float = 0.0

@dataclass
class GridLevel:
    """网格层级"""
    level: int
    buy_price: float
    sell_price: float
    quantity: float
    buy_order: Optional[GridOrder] = None
    sell_order: Optional[GridOrder] = None
    profit: float = 0.0

class GridTradingStrategy:
    """网格交易策略"""
    
    def __init__(self,
                 symbol: str,
                 price_range: Tuple[float, float],
                 grid_count: int = 20,
                 investment: float = 10000,
                 profit_ratio: float = 0.01,
                 base_quantity: float = None):
        """
        初始化网格交易策略
        
        Args:
            symbol: 交易对
            price_range: 价格区间 (最低价, 最高价)
            grid_count: 网格数量
            investment: 投资金额
            profit_ratio: 单网格利润率
            base_quantity: 基础交易量
        """
        self.symbol = symbol
        self.price_range = price_range
        self.grid_count = grid_count
        self.investment = investment
        self.profit_ratio = profit_ratio
        
        # 计算网格参数
        self.price_step = (price_range[1] - price_range[0]) / grid_count
        self.base_quantity = base_quantity or investment / (grid_count * price_range[0])
        
        # 初始化网格
        self.grid_levels: List[GridLevel] = []
        self.active_orders: Dict[str, GridOrder] = {}
        self.filled_orders: List[GridOrder] = []
        
        # 统计信息
        self.total_profit = 0.0
        self.total_trades = 0
        self.current_capital = investment
        self.unrealized_pnl = 0.0
        self.position = 0.0  # 当前持仓
        
        # 日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self._initialize_grid()
    
    def _initialize_grid(self):
        """初始化网格层级"""
        for i in range(self.grid_count):
            level = i + 1
            buy_price = self.price_range[0] + i * self.price_step
            sell_price = buy_price * (1 + self.profit_ratio)
            
            # 确保卖价不超过上限
            if sell_price > self.price_range[1]:
                sell_price = self.price_range[1]
            
            grid_level = GridLevel(
                level=level,
                buy_price=buy_price,
                sell_price=sell_price,
                quantity=self.base_quantity
            )
            
            self.grid_levels.append(grid_level)
        
        self.logger.info(f"初始化网格完成: {len(self.grid_levels)} 个层级")
    
    def get_current_grid_level(self, current_price: float) -> Optional[GridLevel]:
        """获取当前价格对应的网格层级"""
        for grid_level in self.grid_levels:
            if grid_level.buy_price <= current_price <= grid_level.sell_price:
                return grid_level
        return None
    
    def should_place_buy_order(self, current_price: float, grid_level: GridLevel) -> bool:
        """判断是否应该下买单"""
        return (current_price <= grid_level.buy_price and 
                grid_level.buy_order is None and
                self.current_capital >= grid_level.buy_price * grid_level.quantity)
    
    def should_place_sell_order(self, current_price: float, grid_level: GridLevel) -> bool:
        """判断是否应该下卖单"""
        return (current_price >= grid_level.sell_price and 
                grid_level.sell_order is None and
                self.position >= grid_level.quantity)
    
    def place_buy_order(self, grid_level: GridLevel, timestamp: datetime) -> bool:
        """下买单"""
        if self.current_capital < grid_level.buy_price * grid_level.quantity:
            return False
        
        order = GridOrder(
            price=grid_level.buy_price,
            quantity=grid_level.quantity,
            side='buy',
            status='pending',
            order_id=f"buy_{grid_level.level}_{timestamp.timestamp()}"
        )
        
        grid_level.buy_order = order
        self.active_orders[order.order_id] = order
        
        self.logger.info(f"下买单: 价格 {grid_level.buy_price:.2f}, 数量 {grid_level.quantity:.4f}")
        return True
    
    def place_sell_order(self, grid_level: GridLevel, timestamp: datetime) -> bool:
        """下卖单"""
        if self.position < grid_level.quantity:
            return False
        
        order = GridOrder(
            price=grid_level.sell_price,
            quantity=grid_level.quantity,
            side='sell',
            status='pending',
            order_id=f"sell_{grid_level.level}_{timestamp.timestamp()}"
        )
        
        grid_level.sell_order = order
        self.active_orders[order.order_id] = order
        
        self.logger.info(f"下卖单: 价格 {grid_level.sell_price:.2f}, 数量 {grid_level.quantity:.4f}")
        return True
    
    def fill_order(self, order: GridOrder, current_price: float, timestamp: datetime):
        """成交订单"""
        order.status = 'filled'
        order.fill_time = timestamp
        
        if order.side == 'buy':
            # 买入成交
            cost = order.price * order.quantity
            self.current_capital -= cost
            self.position += order.quantity
            self.logger.info(f"买单成交: {order.quantity:.4f} @ {order.price:.2f}")
            
        else:  # sell
            # 卖出成交
            revenue = order.price * order.quantity
            self.current_capital += revenue
            self.position -= order.quantity
            
            # 计算利润
            profit = revenue - (order.quantity * self._get_average_cost())
            order.profit = profit
            self.total_profit += profit
            self.total_trades += 1
            
            self.logger.info(f"卖单成交: {order.quantity:.4f} @ {order.price:.2f}, 利润: {profit:.2f}")
        
        # 移动到已成交订单
        self.filled_orders.append(order)
        if order.order_id in self.active_orders:
            del self.active_orders[order.order_id]
    
    def _get_average_cost(self) -> float:
        """计算平均成本"""
        if not self.filled_orders:
            return 0.0
        
        total_cost = 0.0
        total_quantity = 0.0
        
        for order in self.filled_orders:
            if order.side == 'buy':
                total_cost += order.price * order.quantity
                total_quantity += order.quantity
        
        return total_cost / total_quantity if total_quantity > 0 else 0.0
    
    def update(self, current_price: float, timestamp: datetime):
        """更新策略状态"""
        # 检查订单成交
        orders_to_fill = []
        for order in self.active_orders.values():
            if order.side == 'buy' and current_price <= order.price:
                orders_to_fill.append(order)
            elif order.side == 'sell' and current_price >= order.price:
                orders_to_fill.append(order)
        
        # 执行成交
        for order in orders_to_fill:
            self.fill_order(order, current_price, timestamp)
        
        # 更新网格订单
        for grid_level in self.grid_levels:
            # 检查是否需要下买单
            if self.should_place_buy_order(current_price, grid_level):
                self.place_buy_order(grid_level, timestamp)
            
            # 检查是否需要下卖单
            if self.should_place_sell_order(current_price, grid_level):
                self.place_sell_order(grid_level, timestamp)
            
            # 重置已成交的订单
            if grid_level.buy_order and grid_level.buy_order.status == 'filled':
                grid_level.buy_order = None
            if grid_level.sell_order and grid_level.sell_order.status == 'filled':
                grid_level.sell_order = None
        
        # 更新未实现盈亏
        if self.position > 0:
            avg_cost = self._get_average_cost()
            self.unrealized_pnl = (current_price - avg_cost) * self.position
    
    def get_performance_stats(self) -> Dict:
        """获取策略表现统计"""
        total_value = self.current_capital + (self.position * self._get_average_cost()) + self.unrealized_pnl
        total_return = (total_value - self.investment) / self.investment
        
        win_trades = len([order for order in self.filled_orders if order.side == 'sell' and order.profit > 0])
        total_sell_trades = len([order for order in self.filled_orders if order.side == 'sell'])
        win_rate = win_trades / total_sell_trades if total_sell_trades > 0 else 0
        
        return {
            'total_profit': self.total_profit,
            'unrealized_pnl': self.unrealized_pnl,
            'total_return': total_return,
            'total_trades': self.total_trades,
            'win_rate': win_rate,
            'current_capital': self.current_capital,
            'position': self.position,
            'total_value': total_value,
            'active_orders': len(self.active_orders)
        }
    
    def get_grid_status(self) -> List[Dict]:
        """获取网格状态"""
        status = []
        for grid_level in self.grid_levels:
            status.append({
                'level': grid_level.level,
                'buy_price': grid_level.buy_price,
                'sell_price': grid_level.sell_price,
                'quantity': grid_level.quantity,
                'buy_order_status': grid_level.buy_order.status if grid_level.buy_order else None,
                'sell_order_status': grid_level.sell_order.status if grid_level.sell_order else None,
                'profit': grid_level.profit
            })
        return status

class GridTradingBacktest:
    """网格交易回测引擎"""
    
    def __init__(self, strategy: GridTradingStrategy):
        self.strategy = strategy
        self.equity_curve = [strategy.investment]
        self.timestamps = []
        self.logger = logging.getLogger(__name__)
    
    def run_backtest(self, data: pd.DataFrame) -> Dict:
        """运行回测"""
        self.logger.info(f"开始网格交易回测: {len(data)} 条数据")
        
        for i, (timestamp, row) in enumerate(data.iterrows()):
            current_price = row['close']
            
            # 更新策略
            self.strategy.update(current_price, timestamp)
            
            # 记录资金曲线
            stats = self.strategy.get_performance_stats()
            self.equity_curve.append(stats['total_value'])
            self.timestamps.append(timestamp)
            
            # 进度报告
            if i % 1000 == 0:
                progress = i / len(data) * 100
                self.logger.info(f"回测进度: {progress:.1f}%")
        
        self.logger.info("网格交易回测完成")
        return self.strategy.get_performance_stats()

def create_optimized_grid_strategy(symbol: str, data: pd.DataFrame, investment: float = 10000) -> GridTradingStrategy:
    """创建优化的网格策略"""
    
    # 分析价格数据
    prices = data['close']
    price_min = prices.min()
    price_max = prices.max()
    price_mean = prices.mean()
    price_std = prices.std()
    
    # 设置价格区间（均值 ± 2个标准差）
    lower_bound = max(price_min, price_mean - 2 * price_std)
    upper_bound = min(price_max, price_mean + 2 * price_std)
    
    # 根据波动率调整网格数量
    volatility = prices.pct_change().std()
    if volatility > 0.05:  # 高波动
        grid_count = 30
        profit_ratio = 0.015
    elif volatility > 0.03:  # 中波动
        grid_count = 25
        profit_ratio = 0.012
    else:  # 低波动
        grid_count = 20
        profit_ratio = 0.008
    
    strategy = GridTradingStrategy(
        symbol=symbol,
        price_range=(lower_bound, upper_bound),
        grid_count=grid_count,
        investment=investment,
        profit_ratio=profit_ratio
    )
    
    logging.info(f"创建优化网格策略:")
    logging.info(f"  价格区间: {lower_bound:.2f} - {upper_bound:.2f}")
    logging.info(f"  网格数量: {grid_count}")
    logging.info(f"  利润率: {profit_ratio:.1%}")
    
    return strategy
