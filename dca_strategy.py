"""
DCA定投策略 - 稳定长期收益策略
通过定期定额投资，摊平成本，获取长期趋势收益
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

@dataclass
class DCAOrder:
    """DCA订单记录"""
    timestamp: datetime
    price: float
    amount: float  # 投资金额
    quantity: float  # 购买数量
    order_type: str  # 'regular', 'dip_buy', 'take_profit'
    rsi: float = None
    reason: str = ""

class DCAStrategy:
    """DCA定投策略"""
    
    def __init__(self,
                 symbol: str,
                 total_investment: float = 10000,
                 regular_amount: float = 100,
                 buy_interval_hours: int = 24,
                 rsi_dip_threshold: float = 30,
                 rsi_peak_threshold: float = 70,
                 dip_multiplier: float = 2.0,
                 take_profit_threshold: float = 0.5,
                 stop_loss_threshold: float = -0.3):
        """
        初始化DCA策略
        
        Args:
            symbol: 交易对
            total_investment: 总投资金额
            regular_amount: 定期投资金额
            buy_interval_hours: 买入间隔（小时）
            rsi_dip_threshold: RSI抄底阈值
            rsi_peak_threshold: RSI减仓阈值
            dip_multiplier: 抄底时的投资倍数
            take_profit_threshold: 止盈阈值
            stop_loss_threshold: 止损阈值
        """
        self.symbol = symbol
        self.total_investment = total_investment
        self.regular_amount = regular_amount
        self.buy_interval = timedelta(hours=buy_interval_hours)
        self.rsi_dip_threshold = rsi_dip_threshold
        self.rsi_peak_threshold = rsi_peak_threshold
        self.dip_multiplier = dip_multiplier
        self.take_profit_threshold = take_profit_threshold
        self.stop_loss_threshold = stop_loss_threshold
        
        # 状态变量
        self.remaining_capital = total_investment
        self.total_quantity = 0.0
        self.orders: List[DCAOrder] = []
        self.last_buy_time: Optional[datetime] = None
        
        # 统计信息
        self.total_invested = 0.0
        self.realized_profit = 0.0
        self.unrealized_pnl = 0.0
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0  # 默认中性值
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def get_average_cost(self) -> float:
        """计算平均成本"""
        if self.total_quantity == 0:
            return 0.0
        return self.total_invested / self.total_quantity
    
    def should_buy_regular(self, current_time: datetime) -> bool:
        """判断是否应该定期买入"""
        if self.last_buy_time is None:
            return True
        return current_time - self.last_buy_time >= self.buy_interval
    
    def should_buy_dip(self, rsi: float) -> bool:
        """判断是否应该抄底买入"""
        return rsi < self.rsi_dip_threshold and self.remaining_capital > 0
    
    def should_take_profit(self, current_price: float, rsi: float) -> bool:
        """判断是否应该止盈"""
        if self.total_quantity == 0:
            return False
        
        avg_cost = self.get_average_cost()
        profit_ratio = (current_price - avg_cost) / avg_cost
        
        return (profit_ratio > self.take_profit_threshold or 
                rsi > self.rsi_peak_threshold)
    
    def should_stop_loss(self, current_price: float) -> bool:
        """判断是否应该止损"""
        if self.total_quantity == 0:
            return False
        
        avg_cost = self.get_average_cost()
        loss_ratio = (current_price - avg_cost) / avg_cost
        
        return loss_ratio < self.stop_loss_threshold
    
    def execute_buy(self, current_price: float, current_time: datetime, 
                   amount: float, order_type: str, rsi: float = None, reason: str = ""):
        """执行买入"""
        if amount > self.remaining_capital:
            amount = self.remaining_capital
        
        if amount <= 0:
            return False
        
        quantity = amount / current_price
        
        order = DCAOrder(
            timestamp=current_time,
            price=current_price,
            amount=amount,
            quantity=quantity,
            order_type=order_type,
            rsi=rsi,
            reason=reason
        )
        
        self.orders.append(order)
        self.remaining_capital -= amount
        self.total_invested += amount
        self.total_quantity += quantity
        self.last_buy_time = current_time
        
        self.logger.info(f"买入: {quantity:.4f} @ {current_price:.2f}, 类型: {order_type}, 原因: {reason}")
        return True
    
    def execute_sell(self, current_price: float, current_time: datetime, 
                    sell_ratio: float = 1.0, reason: str = ""):
        """执行卖出"""
        if self.total_quantity == 0:
            return False
        
        sell_quantity = self.total_quantity * sell_ratio
        sell_amount = sell_quantity * current_price
        
        # 计算实现盈亏
        avg_cost = self.get_average_cost()
        profit = (current_price - avg_cost) * sell_quantity
        
        self.realized_profit += profit
        self.remaining_capital += sell_amount
        self.total_quantity -= sell_quantity
        
        # 调整总投资额
        self.total_invested *= (1 - sell_ratio)
        
        self.logger.info(f"卖出: {sell_quantity:.4f} @ {current_price:.2f}, 利润: {profit:.2f}, 原因: {reason}")
        return True
    
    def update(self, current_price: float, current_time: datetime, price_history: pd.Series):
        """更新策略状态"""
        # 计算技术指标
        rsi = self.calculate_rsi(price_history)
        
        # 更新未实现盈亏
        if self.total_quantity > 0:
            avg_cost = self.get_average_cost()
            self.unrealized_pnl = (current_price - avg_cost) * self.total_quantity
        
        # 止损检查
        if self.should_stop_loss(current_price):
            self.execute_sell(current_price, current_time, 1.0, "止损")
            return
        
        # 止盈检查
        if self.should_take_profit(current_price, rsi):
            # 部分止盈
            sell_ratio = 0.3 if rsi > self.rsi_peak_threshold else 0.5
            self.execute_sell(current_price, current_time, sell_ratio, "止盈")
            return
        
        # 抄底买入
        if self.should_buy_dip(rsi):
            amount = min(self.regular_amount * self.dip_multiplier, self.remaining_capital)
            self.execute_buy(current_price, current_time, amount, "dip_buy", rsi, f"RSI抄底: {rsi:.1f}")
            return
        
        # 定期买入
        if self.should_buy_regular(current_time):
            self.execute_buy(current_price, current_time, self.regular_amount, "regular", rsi, "定期投资")
    
    def get_performance_stats(self) -> Dict:
        """获取策略表现统计"""
        total_value = self.remaining_capital + (self.total_quantity * self.get_average_cost()) + self.unrealized_pnl
        total_return = (total_value - self.total_investment) / self.total_investment
        
        investment_ratio = self.total_invested / self.total_investment
        
        # 计算各类订单统计
        regular_orders = [o for o in self.orders if o.order_type == 'regular']
        dip_orders = [o for o in self.orders if o.order_type == 'dip_buy']
        
        return {
            'total_investment': self.total_investment,
            'total_invested': self.total_invested,
            'remaining_capital': self.remaining_capital,
            'investment_ratio': investment_ratio,
            'total_quantity': self.total_quantity,
            'average_cost': self.get_average_cost(),
            'realized_profit': self.realized_profit,
            'unrealized_pnl': self.unrealized_pnl,
            'total_value': total_value,
            'total_return': total_return,
            'total_orders': len(self.orders),
            'regular_orders': len(regular_orders),
            'dip_orders': len(dip_orders)
        }

class DCABacktest:
    """DCA策略回测引擎"""
    
    def __init__(self, strategy: DCAStrategy):
        self.strategy = strategy
        self.equity_curve = [strategy.total_investment]
        self.timestamps = []
        self.logger = logging.getLogger(__name__)
    
    def run_backtest(self, data: pd.DataFrame) -> Dict:
        """运行回测"""
        self.logger.info(f"开始DCA策略回测: {len(data)} 条数据")
        
        for i, (timestamp, row) in enumerate(data.iterrows()):
            current_price = row['close']
            
            # 获取价格历史（用于计算技术指标）
            price_history = data['close'].iloc[:i+1]
            
            # 更新策略
            self.strategy.update(current_price, timestamp, price_history)
            
            # 记录资金曲线
            stats = self.strategy.get_performance_stats()
            current_value = stats['remaining_capital'] + (stats['total_quantity'] * current_price)
            self.equity_curve.append(current_value)
            self.timestamps.append(timestamp)
            
            # 进度报告
            if i % 1000 == 0:
                progress = i / len(data) * 100
                self.logger.info(f"回测进度: {progress:.1f}%")
        
        self.logger.info("DCA策略回测完成")
        return self.strategy.get_performance_stats()

def create_adaptive_dca_strategy(symbol: str, data: pd.DataFrame, 
                               total_investment: float = 10000) -> DCAStrategy:
    """创建自适应DCA策略"""
    
    # 分析市场特征
    prices = data['close']
    returns = prices.pct_change().dropna()
    volatility = returns.std()
    
    # 根据波动率调整参数
    if volatility > 0.05:  # 高波动市场
        buy_interval_hours = 12  # 更频繁买入
        regular_amount = total_investment * 0.005  # 每次0.5%
        dip_multiplier = 3.0  # 抄底时3倍投入
        rsi_dip_threshold = 25  # 更激进的抄底
    elif volatility > 0.03:  # 中波动市场
        buy_interval_hours = 24  # 每日买入
        regular_amount = total_investment * 0.01  # 每次1%
        dip_multiplier = 2.0  # 抄底时2倍投入
        rsi_dip_threshold = 30  # 标准抄底
    else:  # 低波动市场
        buy_interval_hours = 48  # 每两天买入
        regular_amount = total_investment * 0.015  # 每次1.5%
        dip_multiplier = 1.5  # 抄底时1.5倍投入
        rsi_dip_threshold = 35  # 保守抄底
    
    strategy = DCAStrategy(
        symbol=symbol,
        total_investment=total_investment,
        regular_amount=regular_amount,
        buy_interval_hours=buy_interval_hours,
        rsi_dip_threshold=rsi_dip_threshold,
        dip_multiplier=dip_multiplier,
        take_profit_threshold=0.3,  # 30%止盈
        stop_loss_threshold=-0.4   # 40%止损
    )
    
    logging.info(f"创建自适应DCA策略:")
    logging.info(f"  买入间隔: {buy_interval_hours} 小时")
    logging.info(f"  定期金额: {regular_amount:.2f} USDT")
    logging.info(f"  抄底倍数: {dip_multiplier}x")
    logging.info(f"  RSI阈值: {rsi_dip_threshold}")
    
    return strategy
