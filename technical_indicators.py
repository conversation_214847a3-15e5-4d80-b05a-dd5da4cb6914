"""
技术指标计算和交易信号生成
包含RSI、MACD、布林带、EMA等指标
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Tuple
from martingale_strategy import SignalType

class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        self.indicators = {}
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        return ta.momentum.RSIIndicator(data['close'], window=period).rsi()
    
    def calculate_macd(self, data: pd.DataFrame, 
                      fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        macd_indicator = ta.trend.MACD(data['close'], window_fast=fast, window_slow=slow, window_sign=signal)
        return {
            'macd': macd_indicator.macd(),
            'macd_signal': macd_indicator.macd_signal(),
            'macd_histogram': macd_indicator.macd_diff()
        }
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, 
                                 period: int = 20, std: float = 2) -> Dict[str, pd.Series]:
        """计算布林带指标"""
        bb_indicator = ta.volatility.BollingerBands(data['close'], window=period, window_dev=std)
        return {
            'bb_upper': bb_indicator.bollinger_hband(),
            'bb_middle': bb_indicator.bollinger_mavg(),
            'bb_lower': bb_indicator.bollinger_lband(),
            'bb_width': bb_indicator.bollinger_wband(),
            'bb_percent': bb_indicator.bollinger_pband()
        }
    
    def calculate_ema(self, data: pd.DataFrame, periods: List[int]) -> Dict[str, pd.Series]:
        """计算EMA指标"""
        emas = {}
        for period in periods:
            emas[f'ema_{period}'] = ta.trend.EMAIndicator(data['close'], window=period).ema_indicator()
        return emas
    
    def calculate_stochastic(self, data: pd.DataFrame, 
                           k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """计算随机指标"""
        stoch_indicator = ta.momentum.StochasticOscillator(
            data['high'], data['low'], data['close'], 
            window=k_period, smooth_window=d_period
        )
        return {
            'stoch_k': stoch_indicator.stoch(),
            'stoch_d': stoch_indicator.stoch_signal()
        }
    
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR指标"""
        return ta.volatility.AverageTrueRange(data['high'], data['low'], data['close'], window=period).average_true_range()
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """计算成交量指标"""
        return {
            'volume_sma': ta.volume.VolumeSMAIndicator(data['close'], data['volume'], window=20).volume_sma(),
            'volume_ema': ta.volume.VolumeEMAIndicator(data['close'], data['volume'], window=20).volume_ema(),
            'mfi': ta.volume.MFIIndicator(data['high'], data['low'], data['close'], data['volume'], window=14).money_flow_index()
        }

class SignalGenerator:
    """交易信号生成器"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
    
    def generate_rsi_signals(self, data: pd.DataFrame, 
                           oversold: float = 30, overbought: float = 70) -> pd.Series:
        """基于RSI生成信号"""
        rsi = self.indicators.calculate_rsi(data)
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # RSI超卖时买入做多
        signals[rsi < oversold] = SignalType.LONG.value
        # RSI超买时卖出做空
        signals[rsi > overbought] = SignalType.SHORT.value
        
        return signals
    
    def generate_macd_signals(self, data: pd.DataFrame) -> pd.Series:
        """基于MACD生成信号"""
        macd_data = self.indicators.calculate_macd(data)
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # MACD金叉做多
        macd_cross_up = (macd_data['macd'] > macd_data['macd_signal']) & \
                       (macd_data['macd'].shift(1) <= macd_data['macd_signal'].shift(1))
        signals[macd_cross_up] = SignalType.LONG.value
        
        # MACD死叉做空
        macd_cross_down = (macd_data['macd'] < macd_data['macd_signal']) & \
                         (macd_data['macd'].shift(1) >= macd_data['macd_signal'].shift(1))
        signals[macd_cross_down] = SignalType.SHORT.value
        
        return signals
    
    def generate_bollinger_signals(self, data: pd.DataFrame) -> pd.Series:
        """基于布林带生成信号"""
        bb_data = self.indicators.calculate_bollinger_bands(data)
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # 价格触及下轨做多
        touch_lower = data['close'] <= bb_data['bb_lower']
        signals[touch_lower] = SignalType.LONG.value
        
        # 价格触及上轨做空
        touch_upper = data['close'] >= bb_data['bb_upper']
        signals[touch_upper] = SignalType.SHORT.value
        
        return signals
    
    def generate_ema_crossover_signals(self, data: pd.DataFrame, 
                                     fast_period: int = 12, slow_period: int = 26) -> pd.Series:
        """基于EMA交叉生成信号"""
        emas = self.indicators.calculate_ema(data, [fast_period, slow_period])
        fast_ema = emas[f'ema_{fast_period}']
        slow_ema = emas[f'ema_{slow_period}']
        
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # 快线上穿慢线做多
        golden_cross = (fast_ema > slow_ema) & (fast_ema.shift(1) <= slow_ema.shift(1))
        signals[golden_cross] = SignalType.LONG.value
        
        # 快线下穿慢线做空
        death_cross = (fast_ema < slow_ema) & (fast_ema.shift(1) >= slow_ema.shift(1))
        signals[death_cross] = SignalType.SHORT.value
        
        return signals
    
    def generate_stochastic_signals(self, data: pd.DataFrame) -> pd.Series:
        """基于随机指标生成信号"""
        stoch_data = self.indicators.calculate_stochastic(data)
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # K线上穿D线且在超卖区做多
        k_cross_up = (stoch_data['stoch_k'] > stoch_data['stoch_d']) & \
                    (stoch_data['stoch_k'].shift(1) <= stoch_data['stoch_d'].shift(1)) & \
                    (stoch_data['stoch_k'] < 20)
        signals[k_cross_up] = SignalType.LONG.value
        
        # K线下穿D线且在超买区做空
        k_cross_down = (stoch_data['stoch_k'] < stoch_data['stoch_d']) & \
                      (stoch_data['stoch_k'].shift(1) >= stoch_data['stoch_d'].shift(1)) & \
                      (stoch_data['stoch_k'] > 80)
        signals[k_cross_down] = SignalType.SHORT.value
        
        return signals
    
    def generate_combined_signals(self, data: pd.DataFrame, 
                                weights: Dict[str, float] = None) -> pd.Series:
        """组合多个指标生成综合信号"""
        if weights is None:
            weights = {
                'rsi': 0.25,
                'macd': 0.25,
                'bollinger': 0.25,
                'ema': 0.25
            }
        
        # 生成各个指标的信号
        rsi_signals = self.generate_rsi_signals(data)
        macd_signals = self.generate_macd_signals(data)
        bb_signals = self.generate_bollinger_signals(data)
        ema_signals = self.generate_ema_crossover_signals(data)
        
        # 信号评分
        signal_scores = pd.DataFrame(index=data.index)
        signal_scores['rsi'] = rsi_signals.map({
            SignalType.LONG.value: 1, 
            SignalType.SHORT.value: -1, 
            SignalType.HOLD.value: 0
        })
        signal_scores['macd'] = macd_signals.map({
            SignalType.LONG.value: 1, 
            SignalType.SHORT.value: -1, 
            SignalType.HOLD.value: 0
        })
        signal_scores['bollinger'] = bb_signals.map({
            SignalType.LONG.value: 1, 
            SignalType.SHORT.value: -1, 
            SignalType.HOLD.value: 0
        })
        signal_scores['ema'] = ema_signals.map({
            SignalType.LONG.value: 1, 
            SignalType.SHORT.value: -1, 
            SignalType.HOLD.value: 0
        })
        
        # 加权平均
        weighted_score = (signal_scores['rsi'] * weights['rsi'] + 
                         signal_scores['macd'] * weights['macd'] + 
                         signal_scores['bollinger'] * weights['bollinger'] + 
                         signal_scores['ema'] * weights['ema'])
        
        # 生成最终信号
        final_signals = pd.Series(SignalType.HOLD.value, index=data.index)
        final_signals[weighted_score > 0.3] = SignalType.LONG.value
        final_signals[weighted_score < -0.3] = SignalType.SHORT.value
        
        return final_signals
    
    def add_signal_filters(self, data: pd.DataFrame, signals: pd.Series) -> pd.Series:
        """添加信号过滤器"""
        filtered_signals = signals.copy()
        
        # 计算ATR用于波动性过滤
        atr = self.indicators.calculate_atr(data)
        atr_threshold = atr.rolling(20).mean() * 1.5
        
        # 过滤低波动性时期的信号
        low_volatility = atr < atr_threshold
        filtered_signals[low_volatility] = SignalType.HOLD.value
        
        # 成交量过滤
        volume_indicators = self.indicators.calculate_volume_indicators(data)
        volume_ma = data['volume'].rolling(20).mean()
        low_volume = data['volume'] < volume_ma * 0.8
        filtered_signals[low_volume] = SignalType.HOLD.value
        
        return filtered_signals
