"""
马丁格尔量化交易策略
支持10倍杠杆，5%本金开仓，3次加仓，最大持仓3个币种
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class SignalType(Enum):
    """交易信号类型"""
    LONG = "long"
    SHORT = "short"
    CLOSE = "close"
    HOLD = "hold"

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: float
    quantity: float
    leverage: int
    margin_used: float
    unrealized_pnl: float = 0.0
    add_count: int = 0  # 加仓次数
    entry_time: pd.Timestamp = None

@dataclass
class Trade:
    """交易记录"""
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    quantity: float
    leverage: int
    pnl: float
    entry_time: pd.Timestamp
    exit_time: pd.Timestamp
    duration: pd.Timedelta

class MartingaleStrategy:
    """马丁格尔交易策略"""
    
    def __init__(self, 
                 initial_capital: float = 10000,
                 leverage: int = 10,
                 position_size_pct: float = 0.05,  # 5%本金开仓
                 max_add_positions: int = 3,  # 最大加仓次数
                 max_positions: int = 3,  # 最大持仓币种数
                 add_position_threshold: float = 0.02,  # 2%亏损时加仓
                 stop_loss_pct: float = 0.15,  # 15%止损
                 take_profit_pct: float = 0.08):  # 8%止盈
        
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.leverage = leverage
        self.position_size_pct = position_size_pct
        self.max_add_positions = max_add_positions
        self.max_positions = max_positions
        self.add_position_threshold = add_position_threshold
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        
        # 持仓和交易记录
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve: List[float] = [initial_capital]
        self.timestamps: List[pd.Timestamp] = []
        
        # 统计信息
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0.0
        self.peak_equity = initial_capital
        
        # 日志设置
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def calculate_position_size(self, price: float, is_add_position: bool = False) -> float:
        """计算仓位大小"""
        if is_add_position:
            # 加仓时使用更大的仓位
            size_pct = self.position_size_pct * 2
        else:
            size_pct = self.position_size_pct
            
        # 可用资金 = 当前资金 - 已使用保证金
        used_margin = sum(pos.margin_used for pos in self.positions.values())
        available_capital = self.current_capital - used_margin
        
        # 计算仓位大小
        position_value = available_capital * size_pct * self.leverage
        quantity = position_value / price
        
        return quantity
    
    def calculate_margin(self, price: float, quantity: float) -> float:
        """计算所需保证金"""
        position_value = price * quantity
        margin = position_value / self.leverage
        return margin
    
    def update_unrealized_pnl(self, current_prices: Dict[str, float]):
        """更新未实现盈亏"""
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                current_price = current_prices[symbol]
                if position.side == 'long':
                    pnl = (current_price - position.entry_price) * position.quantity
                else:  # short
                    pnl = (position.entry_price - current_price) * position.quantity
                
                position.unrealized_pnl = pnl
    
    def should_add_position(self, symbol: str, current_price: float) -> bool:
        """判断是否应该加仓"""
        if symbol not in self.positions:
            return False
            
        position = self.positions[symbol]
        if position.add_count >= self.max_add_positions:
            return False
        
        # 计算当前亏损比例
        if position.side == 'long':
            loss_pct = (position.entry_price - current_price) / position.entry_price
        else:  # short
            loss_pct = (current_price - position.entry_price) / position.entry_price
        
        return loss_pct >= self.add_position_threshold
    
    def should_close_position(self, symbol: str, current_price: float) -> bool:
        """判断是否应该平仓（止损或止盈）"""
        if symbol not in self.positions:
            return False
            
        position = self.positions[symbol]
        
        if position.side == 'long':
            # 多头：当前价格相对入场价格的变化
            pnl_pct = (current_price - position.entry_price) / position.entry_price
        else:  # short
            # 空头：入场价格相对当前价格的变化
            pnl_pct = (position.entry_price - current_price) / position.entry_price
        
        # 止损
        if pnl_pct <= -self.stop_loss_pct:
            return True
            
        # 止盈
        if pnl_pct >= self.take_profit_pct:
            return True
            
        return False
    
    def open_position(self, symbol: str, side: str, price: float, timestamp: pd.Timestamp) -> bool:
        """开仓"""
        # 检查是否已达到最大持仓数
        if len(self.positions) >= self.max_positions and symbol not in self.positions:
            self.logger.warning(f"已达到最大持仓数 {self.max_positions}")
            return False
        
        # 计算仓位大小
        is_add = symbol in self.positions
        quantity = self.calculate_position_size(price, is_add)
        margin_needed = self.calculate_margin(price, quantity)
        
        # 检查保证金是否足够
        used_margin = sum(pos.margin_used for pos in self.positions.values())
        if used_margin + margin_needed > self.current_capital * 0.9:  # 保留10%缓冲
            self.logger.warning(f"保证金不足，无法开仓 {symbol}")
            return False
        
        if symbol in self.positions:
            # 加仓
            position = self.positions[symbol]
            if position.side != side:
                self.logger.warning(f"加仓方向不一致 {symbol}")
                return False
                
            # 更新平均入场价格
            total_quantity = position.quantity + quantity
            total_value = position.entry_price * position.quantity + price * quantity
            new_avg_price = total_value / total_quantity
            
            position.entry_price = new_avg_price
            position.quantity = total_quantity
            position.margin_used += margin_needed
            position.add_count += 1
            
            self.logger.info(f"加仓 {symbol} {side} 价格:{price:.4f} 数量:{quantity:.4f} 加仓次数:{position.add_count}")
        else:
            # 新开仓
            self.positions[symbol] = Position(
                symbol=symbol,
                side=side,
                entry_price=price,
                quantity=quantity,
                leverage=self.leverage,
                margin_used=margin_needed,
                entry_time=timestamp
            )
            self.logger.info(f"开仓 {symbol} {side} 价格:{price:.4f} 数量:{quantity:.4f}")
        
        return True
    
    def close_position(self, symbol: str, price: float, timestamp: pd.Timestamp) -> bool:
        """平仓"""
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        
        # 计算盈亏
        if position.side == 'long':
            pnl = (price - position.entry_price) * position.quantity
        else:  # short
            pnl = (position.entry_price - price) * position.quantity
        
        # 更新资金
        self.current_capital += pnl
        
        # 记录交易
        trade = Trade(
            symbol=symbol,
            side=position.side,
            entry_price=position.entry_price,
            exit_price=price,
            quantity=position.quantity,
            leverage=self.leverage,
            pnl=pnl,
            entry_time=position.entry_time,
            exit_time=timestamp,
            duration=timestamp - position.entry_time
        )
        self.trades.append(trade)
        
        # 更新统计
        self.total_trades += 1
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # 删除持仓
        del self.positions[symbol]
        
        self.logger.info(f"平仓 {symbol} 价格:{price:.4f} 盈亏:{pnl:.2f} 当前资金:{self.current_capital:.2f}")
        return True
    
    def update_equity_curve(self, timestamp: pd.Timestamp, current_prices: Dict[str, float]):
        """更新资金曲线"""
        # 更新未实现盈亏
        self.update_unrealized_pnl(current_prices)
        
        # 计算当前净值
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        current_equity = self.current_capital + unrealized_pnl
        
        self.equity_curve.append(current_equity)
        self.timestamps.append(timestamp)
        
        # 更新最大回撤
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
        else:
            drawdown = (self.peak_equity - current_equity) / self.peak_equity
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
    
    def get_performance_stats(self) -> Dict:
        """获取策略表现统计"""
        if not self.trades:
            return {}
        
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        
        profits = [trade.pnl for trade in self.trades if trade.pnl > 0]
        losses = [trade.pnl for trade in self.trades if trade.pnl < 0]
        
        avg_profit = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        profit_factor = abs(sum(profits) / sum(losses)) if losses else float('inf')
        
        return {
            'total_return': total_return,
            'total_trades': self.total_trades,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown,
            'profit_factor': profit_factor,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'final_capital': self.current_capital
        }

class BacktestEngine:
    """回测引擎"""

    def __init__(self, strategy: MartingaleStrategy, signal_generator):
        self.strategy = strategy
        self.signal_generator = signal_generator
        self.logger = logging.getLogger(__name__)

    def run_backtest(self, data_dict: Dict[str, pd.DataFrame],
                    start_date: str = None, end_date: str = None) -> Dict:
        """
        运行回测

        Args:
            data_dict: 包含多个交易对数据的字典
            start_date: 开始日期
            end_date: 结束日期
        """
        self.logger.info("开始回测...")

        # 获取所有时间戳的并集
        all_timestamps = set()
        for data in data_dict.values():
            if not data.empty:
                all_timestamps.update(data.index)

        timestamps = sorted(list(all_timestamps))

        # 过滤时间范围
        if start_date:
            start_dt = pd.to_datetime(start_date)
            timestamps = [ts for ts in timestamps if ts >= start_dt]

        if end_date:
            end_dt = pd.to_datetime(end_date)
            timestamps = [ts for ts in timestamps if ts <= end_dt]

        self.logger.info(f"回测时间范围: {timestamps[0]} 到 {timestamps[-1]}")
        self.logger.info(f"总时间点数: {len(timestamps)}")

        # 逐个时间点进行回测
        for i, timestamp in enumerate(timestamps):
            if i % 1000 == 0:
                progress = i / len(timestamps) * 100
                self.logger.info(f"回测进度: {progress:.1f}% ({i}/{len(timestamps)})")

            # 获取当前时间点的价格
            current_prices = {}
            for symbol, data in data_dict.items():
                if timestamp in data.index:
                    current_prices[symbol] = data.loc[timestamp, 'close']

            # 处理每个交易对
            for symbol in current_prices.keys():
                if symbol not in data_dict or data_dict[symbol].empty:
                    continue

                # 获取到当前时间点的历史数据
                historical_data = data_dict[symbol][data_dict[symbol].index <= timestamp]

                if len(historical_data) < 50:  # 需要足够的历史数据
                    continue

                # 生成交易信号
                try:
                    signals = self.signal_generator.generate_combined_signals(historical_data)
                    if timestamp not in signals.index:
                        continue

                    current_signal = signals.loc[timestamp]
                    current_price = current_prices[symbol]

                    # 执行交易逻辑
                    self._execute_trading_logic(symbol, current_signal, current_price, timestamp)

                except Exception as e:
                    self.logger.warning(f"处理 {symbol} 信号时出错: {e}")
                    continue

            # 更新资金曲线
            self.strategy.update_equity_curve(timestamp, current_prices)

        self.logger.info("回测完成")
        return self.strategy.get_performance_stats()

    def _execute_trading_logic(self, symbol: str, signal: str, price: float, timestamp: pd.Timestamp):
        """执行交易逻辑"""

        # 检查是否需要平仓
        if self.strategy.should_close_position(symbol, price):
            self.strategy.close_position(symbol, price, timestamp)
            return

        # 检查是否需要加仓
        if self.strategy.should_add_position(symbol, price):
            if signal in ['long', 'short']:
                side = signal
                self.strategy.open_position(symbol, side, price, timestamp)
            return

        # 新开仓逻辑
        if signal == 'long' and symbol not in self.strategy.positions:
            if len(self.strategy.positions) < self.strategy.max_positions:
                self.strategy.open_position(symbol, 'long', price, timestamp)

        elif signal == 'short' and symbol not in self.strategy.positions:
            if len(self.strategy.positions) < self.strategy.max_positions:
                self.strategy.open_position(symbol, 'short', price, timestamp)
