"""
马丁格尔量化交易策略主程序
整合所有模块，运行完整的回测分析
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import os
import json

# 导入自定义模块
from martingale_strategy import MartingaleStrategy, BacktestEngine
from technical_indicators import SignalGenerator
from data_fetcher import DataFetcher
from symbol_analyzer import SymbolAnalyzer, get_best_crypto_pairs_for_martingale
from risk_management import RiskManager
from performance_analyzer import PerformanceAnalyzer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('backtest.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始马丁格尔量化交易策略回测")
    
    # 策略参数
    strategy_params = {
        'initial_capital': 10000,      # 10000 USDT
        'leverage': 10,                # 10倍杠杆
        'position_size_pct': 0.05,     # 5%本金开仓
        'max_add_positions': 3,        # 最大加仓3次
        'max_positions': 3,            # 最大持仓3个币种
        'add_position_threshold': 0.02, # 2%亏损时加仓
        'stop_loss_pct': 0.15,         # 15%止损
        'take_profit_pct': 0.08        # 8%止盈
    }
    
    try:
        # 1. 初始化数据获取器
        logger.info("初始化数据获取器...")
        data_fetcher = DataFetcher(exchange_name='binance')
        
        # 2. 分析和选择最佳交易对
        logger.info("分析交易对适合性...")
        symbol_analyzer = SymbolAnalyzer(data_fetcher)
        
        # 获取推荐的交易对
        recommended_symbols = get_best_crypto_pairs_for_martingale()[:10]  # 取前10个
        logger.info(f"推荐交易对: {recommended_symbols}")
        
        # 分析交易对
        analysis_results = symbol_analyzer.analyze_multiple_symbols(
            recommended_symbols, 
            timeframe='1h', 
            days=365
        )
        
        # 选择最适合的交易对
        suitable_symbols = [result['symbol'] for result in analysis_results 
                          if result['suitable']][:strategy_params['max_positions']]
        
        if not suitable_symbols:
            logger.error("没有找到适合的交易对")
            return
        
        logger.info(f"选择的交易对: {suitable_symbols}")
        
        # 3. 获取历史数据
        logger.info("获取历史数据...")
        data_dict = data_fetcher.get_multiple_symbols_data(
            suitable_symbols, 
            timeframe='1h', 
            days=365
        )
        
        if not data_dict:
            logger.error("未能获取到历史数据")
            return
        
        logger.info(f"成功获取 {len(data_dict)} 个交易对的数据")
        
        # 4. 初始化策略和相关组件
        logger.info("初始化策略组件...")
        strategy = MartingaleStrategy(**strategy_params)
        signal_generator = SignalGenerator()
        risk_manager = RiskManager()
        performance_analyzer = PerformanceAnalyzer()
        
        # 5. 运行回测
        logger.info("开始回测...")
        backtest_engine = BacktestEngine(strategy, signal_generator)
        
        # 设置回测时间范围（最近一年）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        performance_stats = backtest_engine.run_backtest(
            data_dict,
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        # 6. 计算详细性能指标
        logger.info("计算性能指标...")
        detailed_metrics = performance_analyzer.calculate_performance_metrics(
            strategy.equity_curve,
            strategy.trades,
            strategy.timestamps,
            strategy_params['initial_capital']
        )
        
        # 7. 生成报告
        logger.info("生成回测报告...")
        
        # 生成文本报告
        report = performance_analyzer.generate_performance_report(
            detailed_metrics, 
            strategy_params
        )
        
        # 保存报告
        with open('backtest_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 生成图表
        equity_fig = performance_analyzer.plot_equity_curve(
            strategy.equity_curve,
            strategy.timestamps,
            strategy_params['initial_capital'],
            'equity_curve.html'
        )
        
        trade_fig = performance_analyzer.plot_trade_analysis(
            strategy.trades,
            'trade_analysis.html'
        )
        
        # 保存详细数据
        results_data = {
            'strategy_params': strategy_params,
            'selected_symbols': suitable_symbols,
            'performance_stats': performance_stats,
            'detailed_metrics': detailed_metrics,
            'symbol_analysis': analysis_results
        }
        
        with open('backtest_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, default=str)
        
        # 8. 输出关键结果
        logger.info("回测完成！")
        print("\n" + "="*60)
        print("马丁格尔策略回测结果摘要")
        print("="*60)
        print(f"初始资金: {strategy_params['initial_capital']:,} USDT")
        print(f"最终资金: {strategy.current_capital:,.2f} USDT")
        print(f"总收益率: {detailed_metrics.get('total_return', 0):.2%}")
        print(f"年化收益率: {detailed_metrics.get('annual_return', 0):.2%}")
        print(f"最大回撤: {detailed_metrics.get('max_drawdown', 0):.2%}")
        print(f"夏普比率: {detailed_metrics.get('sharpe_ratio', 0):.3f}")
        
        trade_stats = detailed_metrics.get('trade_statistics', {})
        if trade_stats:
            print(f"总交易次数: {trade_stats.get('total_trades', 0)}")
            print(f"胜率: {trade_stats.get('win_rate', 0):.2%}")
            print(f"盈亏比: {trade_stats.get('profit_factor', 0):.2f}")
        
        print(f"使用交易对: {', '.join(suitable_symbols)}")
        print("="*60)
        
        print(f"\n详细报告已保存到: backtest_report.txt")
        print(f"资金曲线图: equity_curve.html")
        print(f"交易分析图: trade_analysis.html")
        print(f"完整数据: backtest_results.json")
        
    except Exception as e:
        logger.error(f"回测过程中发生错误: {e}")
        raise

def run_symbol_analysis_only():
    """仅运行交易对分析"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始交易对分析...")
    
    try:
        data_fetcher = DataFetcher(exchange_name='binance')
        symbol_analyzer = SymbolAnalyzer(data_fetcher)
        
        # 获取推荐交易对
        recommended_symbols = get_best_crypto_pairs_for_martingale()
        
        # 分析交易对
        analysis_results = symbol_analyzer.analyze_multiple_symbols(
            recommended_symbols, 
            timeframe='1h', 
            days=365
        )
        
        # 生成分析报告
        report = symbol_analyzer.generate_symbol_report(analysis_results)
        
        # 保存报告
        with open('symbol_analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"\n分析报告已保存到: symbol_analysis_report.txt")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'analyze':
        # 仅运行交易对分析
        run_symbol_analysis_only()
    else:
        # 运行完整回测
        main()
