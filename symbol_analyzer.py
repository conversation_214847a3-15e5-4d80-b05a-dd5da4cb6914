"""
加密货币交易对分析和选择模块
专门为马丁格尔策略选择最适合的交易对
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging
from data_fetcher import DataFetcher

class SymbolAnalyzer:
    """交易对分析器"""
    
    def __init__(self, data_fetcher: DataFetcher):
        self.data_fetcher = data_fetcher
        self.logger = logging.getLogger(__name__)
    
    def calculate_volatility_metrics(self, data: pd.DataFrame) -> Dict:
        """计算波动性指标"""
        if data.empty:
            return {}
        
        returns = data['close'].pct_change().dropna()
        
        return {
            'daily_volatility': returns.std() * np.sqrt(24),
            'weekly_volatility': returns.std() * np.sqrt(24 * 7),
            'monthly_volatility': returns.std() * np.sqrt(24 * 30),
            'max_daily_return': returns.max(),
            'min_daily_return': returns.min(),
            'volatility_of_volatility': returns.rolling(24).std().std(),
            'skewness': returns.skew(),
            'kurtosis': returns.kurtosis()
        }
    
    def calculate_liquidity_metrics(self, data: pd.DataFrame) -> Dict:
        """计算流动性指标"""
        if data.empty:
            return {}
        
        return {
            'avg_volume': data['volume'].mean(),
            'median_volume': data['volume'].median(),
            'volume_std': data['volume'].std(),
            'volume_consistency': data['volume'].std() / data['volume'].mean(),
            'min_volume': data['volume'].min(),
            'volume_trend': np.polyfit(range(len(data)), data['volume'], 1)[0]
        }
    
    def calculate_price_stability(self, data: pd.DataFrame) -> Dict:
        """计算价格稳定性指标"""
        if data.empty:
            return {}
        
        prices = data['close']
        returns = prices.pct_change().dropna()
        
        # 计算价格通道
        rolling_max = prices.rolling(168).max()  # 7天最高价
        rolling_min = prices.rolling(168).min()   # 7天最低价
        price_channel_width = (rolling_max - rolling_min) / prices
        
        return {
            'total_return': (prices.iloc[-1] - prices.iloc[0]) / prices.iloc[0],
            'max_drawdown': self._calculate_max_drawdown(prices),
            'price_channel_width': price_channel_width.mean(),
            'trend_strength': abs(np.polyfit(range(len(prices)), prices, 1)[0]),
            'mean_reversion_tendency': self._calculate_mean_reversion(returns),
            'consecutive_moves': self._calculate_consecutive_moves(returns)
        }
    
    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """计算最大回撤"""
        peak = prices.expanding().max()
        drawdown = (prices - peak) / peak
        return drawdown.min()
    
    def _calculate_mean_reversion(self, returns: pd.Series) -> float:
        """计算均值回归倾向"""
        # 计算连续收益的自相关性
        autocorr = returns.autocorr(lag=1)
        return -autocorr if not np.isnan(autocorr) else 0
    
    def _calculate_consecutive_moves(self, returns: pd.Series) -> float:
        """计算连续同向移动的平均长度"""
        signs = np.sign(returns)
        sign_changes = signs != signs.shift(1)
        consecutive_lengths = []
        
        current_length = 1
        for change in sign_changes[1:]:
            if change:
                consecutive_lengths.append(current_length)
                current_length = 1
            else:
                current_length += 1
        
        return np.mean(consecutive_lengths) if consecutive_lengths else 1
    
    def score_symbol_for_martingale(self, symbol: str, data: pd.DataFrame) -> Dict:
        """为马丁格尔策略评分交易对"""
        if data.empty:
            return {'symbol': symbol, 'score': 0, 'suitable': False, 'reason': '无数据'}
        
        volatility_metrics = self.calculate_volatility_metrics(data)
        liquidity_metrics = self.calculate_liquidity_metrics(data)
        stability_metrics = self.calculate_price_stability(data)
        
        score = 0
        reasons = []
        
        # 1. 波动性评分 (30分)
        daily_vol = volatility_metrics.get('daily_volatility', 0)
        if 0.015 <= daily_vol <= 0.06:  # 理想波动性范围
            vol_score = 30
            reasons.append(f"波动性理想 ({daily_vol:.3f})")
        elif 0.01 <= daily_vol <= 0.08:
            vol_score = 20
            reasons.append(f"波动性可接受 ({daily_vol:.3f})")
        elif daily_vol < 0.01:
            vol_score = 5
            reasons.append(f"波动性过低 ({daily_vol:.3f})")
        else:
            vol_score = 10
            reasons.append(f"波动性过高 ({daily_vol:.3f})")
        score += vol_score
        
        # 2. 流动性评分 (25分)
        avg_volume = liquidity_metrics.get('avg_volume', 0)
        if avg_volume > 5000000:  # 500万USDT
            liq_score = 25
            reasons.append("流动性优秀")
        elif avg_volume > 1000000:  # 100万USDT
            liq_score = 20
            reasons.append("流动性良好")
        elif avg_volume > 500000:   # 50万USDT
            liq_score = 15
            reasons.append("流动性一般")
        else:
            liq_score = 5
            reasons.append("流动性不足")
        score += liq_score
        
        # 3. 价格稳定性评分 (25分)
        max_drawdown = abs(stability_metrics.get('max_drawdown', 0))
        if max_drawdown < 0.3:  # 最大回撤小于30%
            stab_score = 25
            reasons.append(f"价格稳定 (最大回撤: {max_drawdown:.1%})")
        elif max_drawdown < 0.5:
            stab_score = 20
            reasons.append(f"价格较稳定 (最大回撤: {max_drawdown:.1%})")
        elif max_drawdown < 0.7:
            stab_score = 15
            reasons.append(f"价格波动较大 (最大回撤: {max_drawdown:.1%})")
        else:
            stab_score = 5
            reasons.append(f"价格极不稳定 (最大回撤: {max_drawdown:.1%})")
        score += stab_score
        
        # 4. 均值回归倾向评分 (20分)
        mean_reversion = stability_metrics.get('mean_reversion_tendency', 0)
        if mean_reversion > 0.1:
            mr_score = 20
            reasons.append("强均值回归特性")
        elif mean_reversion > 0.05:
            mr_score = 15
            reasons.append("中等均值回归特性")
        elif mean_reversion > 0:
            mr_score = 10
            reasons.append("弱均值回归特性")
        else:
            mr_score = 5
            reasons.append("趋势性较强")
        score += mr_score
        
        return {
            'symbol': symbol,
            'score': score,
            'suitable': score >= 60,
            'volatility_metrics': volatility_metrics,
            'liquidity_metrics': liquidity_metrics,
            'stability_metrics': stability_metrics,
            'reasons': reasons
        }
    
    def analyze_multiple_symbols(self, symbols: List[str], 
                                timeframe: str = '1h', 
                                days: int = 365) -> List[Dict]:
        """分析多个交易对"""
        results = []
        
        self.logger.info(f"开始分析 {len(symbols)} 个交易对...")
        
        for i, symbol in enumerate(symbols):
            self.logger.info(f"分析进度: {i+1}/{len(symbols)} - {symbol}")
            
            try:
                data = self.data_fetcher.fetch_historical_data(symbol, timeframe, days)
                analysis = self.score_symbol_for_martingale(symbol, data)
                results.append(analysis)
                
            except Exception as e:
                self.logger.error(f"分析 {symbol} 失败: {e}")
                results.append({
                    'symbol': symbol,
                    'score': 0,
                    'suitable': False,
                    'reasons': [f"分析失败: {e}"]
                })
        
        # 按评分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        self.logger.info("分析完成")
        return results
    
    def get_top_symbols_for_martingale(self, top_n: int = 10) -> List[Dict]:
        """获取最适合马丁格尔策略的前N个交易对"""
        # 获取推荐的交易对
        recommended_symbols = self.data_fetcher.get_recommended_symbols()
        
        # 如果推荐列表不够，获取高成交量交易对
        if len(recommended_symbols) < top_n * 2:
            volume_symbols = self.data_fetcher.get_top_volume_symbols(top_n=50)
            all_symbols = list(set(recommended_symbols + volume_symbols))
        else:
            all_symbols = recommended_symbols
        
        # 分析所有交易对
        analysis_results = self.analyze_multiple_symbols(all_symbols)
        
        # 过滤出适合的交易对
        suitable_symbols = [result for result in analysis_results if result['suitable']]
        
        # 返回前N个
        return suitable_symbols[:top_n]
    
    def generate_symbol_report(self, analysis_results: List[Dict]) -> str:
        """生成交易对分析报告"""
        report = "=== 马丁格尔策略交易对分析报告 ===\n\n"
        
        suitable_count = sum(1 for result in analysis_results if result['suitable'])
        report += f"总分析交易对数: {len(analysis_results)}\n"
        report += f"适合交易对数: {suitable_count}\n"
        report += f"适合率: {suitable_count/len(analysis_results)*100:.1f}%\n\n"
        
        report += "=== 推荐交易对 (评分 >= 60) ===\n"
        for result in analysis_results:
            if result['suitable']:
                report += f"\n{result['symbol']} - 评分: {result['score']}\n"
                for reason in result['reasons']:
                    report += f"  • {reason}\n"
        
        report += "\n=== 不推荐交易对 (评分 < 60) ===\n"
        for result in analysis_results:
            if not result['suitable']:
                report += f"\n{result['symbol']} - 评分: {result['score']}\n"
                for reason in result['reasons']:
                    report += f"  • {reason}\n"
        
        return report

def get_best_crypto_pairs_for_martingale() -> List[str]:
    """获取最适合马丁格尔策略的加密货币对"""
    
    # 基于历史表现和特性分析的推荐列表
    # 这些币种具有良好的流动性、适中的波动性和一定的均值回归特性
    
    tier1_pairs = [
        'BTC/USDT',   # 比特币 - 流动性最好，波动适中
        'ETH/USDT',   # 以太坊 - 流动性优秀，技术面清晰
        'BNB/USDT',   # 币安币 - 稳定增长，波动可控
    ]
    
    tier2_pairs = [
        'ADA/USDT',   # 卡尔达诺 - 均值回归特性明显
        'DOT/USDT',   # 波卡 - 技术面良好
        'MATIC/USDT', # Polygon - 生态发展稳定
        'LINK/USDT',  # Chainlink - 基本面扎实
        'SOL/USDT',   # Solana - 高性能公链
    ]
    
    tier3_pairs = [
        'AVAX/USDT',  # 雪崩协议
        'ATOM/USDT',  # Cosmos
        'UNI/USDT',   # Uniswap
        'LTC/USDT',   # 莱特币
        'BCH/USDT',   # 比特币现金
        'VET/USDT',   # 唯链
        'ALGO/USDT',  # Algorand
    ]
    
    return tier1_pairs + tier2_pairs + tier3_pairs
