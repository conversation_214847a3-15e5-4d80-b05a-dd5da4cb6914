"""
策略对比演示程序
比较马丁格尔、网格交易、DCA等策略的表现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 导入策略模块
from martingale_strategy import MartingaleStrategy, BacktestEngine
from grid_trading_strategy import GridTradingStrategy, GridTradingBacktest, create_optimized_grid_strategy
from dca_strategy import DCAStrategy, DCABacktest, create_adaptive_dca_strategy
from technical_indicators import SignalGenerator
from demo_backtest import generate_mock_data, MockSignalGenerator

def run_strategy_comparison():
    """运行策略对比"""
    print("=" * 80)
    print("加密货币交易策略对比分析")
    print("=" * 80)
    
    # 生成模拟数据
    print("生成模拟数据...")
    symbol = 'BTC/USDT'
    data = generate_mock_data(symbol, days=365, volatility=0.035)
    print(f"数据量: {len(data)} 条")
    print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
    print()
    
    investment = 10000  # 统一投资金额
    results = {}
    
    # 1. 马丁格尔策略
    print("1. 测试马丁格尔策略...")
    try:
        martingale_strategy = MartingaleStrategy(
            initial_capital=investment,
            leverage=5,  # 降低杠杆
            position_size_pct=0.03,  # 降低仓位
            max_add_positions=2,  # 减少加仓次数
            max_positions=1,  # 只交易一个币种
            stop_loss_pct=0.10,  # 收紧止损
            take_profit_pct=0.06
        )
        
        signal_generator = MockSignalGenerator()
        backtest_engine = BacktestEngine(martingale_strategy, signal_generator)
        
        # 运行回测
        data_dict = {symbol: data}
        martingale_stats = backtest_engine.run_backtest(data_dict)
        
        results['马丁格尔策略'] = {
            'final_capital': martingale_strategy.current_capital,
            'total_return': (martingale_strategy.current_capital - investment) / investment,
            'max_drawdown': martingale_stats.get('max_drawdown', 0),
            'total_trades': martingale_stats.get('total_trades', 0),
            'win_rate': martingale_stats.get('win_rate', 0),
            'equity_curve': martingale_strategy.equity_curve
        }
        print(f"  最终资金: {martingale_strategy.current_capital:.2f} USDT")
        print(f"  总收益率: {results['马丁格尔策略']['total_return']:.2%}")
        
    except Exception as e:
        print(f"  马丁格尔策略测试失败: {e}")
        results['马丁格尔策略'] = None
    
    # 2. 网格交易策略
    print("\n2. 测试网格交易策略...")
    try:
        grid_strategy = create_optimized_grid_strategy(symbol, data, investment)
        grid_backtest = GridTradingBacktest(grid_strategy)
        grid_stats = grid_backtest.run_backtest(data)
        
        results['网格交易策略'] = {
            'final_capital': grid_stats['total_value'],
            'total_return': grid_stats['total_return'],
            'max_drawdown': 0,  # 网格策略通常回撤较小
            'total_trades': grid_stats['total_trades'],
            'win_rate': grid_stats['win_rate'],
            'equity_curve': grid_backtest.equity_curve
        }
        print(f"  最终资金: {grid_stats['total_value']:.2f} USDT")
        print(f"  总收益率: {grid_stats['total_return']:.2%}")
        print(f"  总交易次数: {grid_stats['total_trades']}")
        
    except Exception as e:
        print(f"  网格交易策略测试失败: {e}")
        results['网格交易策略'] = None
    
    # 3. DCA定投策略
    print("\n3. 测试DCA定投策略...")
    try:
        dca_strategy = create_adaptive_dca_strategy(symbol, data, investment)
        dca_backtest = DCABacktest(dca_strategy)
        dca_stats = dca_backtest.run_backtest(data)
        
        # 计算最终价值（包括持仓）
        final_price = data['close'].iloc[-1]
        final_value = dca_stats['remaining_capital'] + (dca_stats['total_quantity'] * final_price)
        
        results['DCA定投策略'] = {
            'final_capital': final_value,
            'total_return': (final_value - investment) / investment,
            'max_drawdown': 0,  # DCA策略回撤相对较小
            'total_trades': dca_stats['total_orders'],
            'win_rate': 1.0 if final_value > investment else 0.0,  # 简化计算
            'equity_curve': dca_backtest.equity_curve
        }
        print(f"  最终资金: {final_value:.2f} USDT")
        print(f"  总收益率: {results['DCA定投策略']['total_return']:.2%}")
        print(f"  投资次数: {dca_stats['total_orders']}")
        print(f"  平均成本: {dca_stats['average_cost']:.2f}")
        
    except Exception as e:
        print(f"  DCA定投策略测试失败: {e}")
        results['DCA定投策略'] = None
    
    # 4. 简单持有策略（基准）
    print("\n4. 测试简单持有策略...")
    try:
        initial_price = data['close'].iloc[0]
        final_price = data['close'].iloc[-1]
        quantity = investment / initial_price
        final_value = quantity * final_price
        
        results['简单持有策略'] = {
            'final_capital': final_value,
            'total_return': (final_value - investment) / investment,
            'max_drawdown': (data['close'].min() - initial_price) / initial_price,
            'total_trades': 1,
            'win_rate': 1.0 if final_value > investment else 0.0,
            'equity_curve': [investment * (price / initial_price) for price in data['close']]
        }
        print(f"  最终资金: {final_value:.2f} USDT")
        print(f"  总收益率: {results['简单持有策略']['total_return']:.2%}")
        
    except Exception as e:
        print(f"  简单持有策略测试失败: {e}")
        results['简单持有策略'] = None
    
    # 生成对比报告
    print("\n" + "=" * 80)
    print("策略对比结果")
    print("=" * 80)
    
    # 按收益率排序
    valid_results = {k: v for k, v in results.items() if v is not None}
    sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['total_return'], reverse=True)
    
    print(f"{'策略名称':<15} {'最终资金':<12} {'总收益率':<10} {'交易次数':<8} {'胜率':<8}")
    print("-" * 60)
    
    for strategy_name, stats in sorted_results:
        print(f"{strategy_name:<15} "
              f"{stats['final_capital']:>10.2f} "
              f"{stats['total_return']:>8.2%} "
              f"{stats['total_trades']:>6} "
              f"{stats['win_rate']:>6.1%}")
    
    # 风险调整收益分析
    print("\n风险调整收益分析:")
    print(f"{'策略名称':<15} {'夏普比率':<10} {'最大回撤':<10} {'风险评级':<10}")
    print("-" * 50)
    
    for strategy_name, stats in sorted_results:
        # 简化的夏普比率计算
        if len(stats['equity_curve']) > 1:
            returns = pd.Series(stats['equity_curve']).pct_change().dropna()
            sharpe = returns.mean() / returns.std() * np.sqrt(365) if returns.std() > 0 else 0
        else:
            sharpe = 0
        
        # 风险评级
        if abs(stats['max_drawdown']) < 0.1 and stats['total_return'] > 0:
            risk_rating = "低风险"
        elif abs(stats['max_drawdown']) < 0.2 and stats['total_return'] > -0.1:
            risk_rating = "中风险"
        else:
            risk_rating = "高风险"
        
        print(f"{strategy_name:<15} "
              f"{sharpe:>8.2f} "
              f"{stats['max_drawdown']:>8.2%} "
              f"{risk_rating:<10}")
    
    # 策略推荐
    print("\n" + "=" * 80)
    print("策略推荐")
    print("=" * 80)
    
    if valid_results:
        best_strategy = sorted_results[0]
        print(f"🏆 最佳收益策略: {best_strategy[0]}")
        print(f"   收益率: {best_strategy[1]['total_return']:.2%}")
        print(f"   最终资金: {best_strategy[1]['final_capital']:.2f} USDT")
        
        # 根据不同风险偏好推荐
        print(f"\n📊 不同风险偏好推荐:")
        
        # 保守型投资者
        conservative_strategies = [s for s in sorted_results 
                                 if abs(s[1]['max_drawdown']) < 0.15 and s[1]['total_return'] > 0]
        if conservative_strategies:
            print(f"   保守型: {conservative_strategies[0][0]} (低风险，稳定收益)")
        
        # 平衡型投资者
        balanced_strategies = [s for s in sorted_results 
                             if abs(s[1]['max_drawdown']) < 0.25 and s[1]['total_return'] > 0.05]
        if balanced_strategies:
            print(f"   平衡型: {balanced_strategies[0][0]} (中等风险，较好收益)")
        
        # 激进型投资者
        aggressive_strategies = [s for s in sorted_results if s[1]['total_return'] > 0.1]
        if aggressive_strategies:
            print(f"   激进型: {aggressive_strategies[0][0]} (高收益潜力)")
    
    print(f"\n💡 策略选择建议:")
    print(f"   1. 新手投资者: 推荐DCA定投策略，操作简单，风险可控")
    print(f"   2. 有经验投资者: 推荐网格交易策略，收益稳定，适合震荡市")
    print(f"   3. 专业投资者: 可考虑组合策略，分散风险，提高收益")
    print(f"   4. 风险提醒: 马丁格尔策略风险较高，需谨慎使用")
    
    # 绘制资金曲线对比图
    try:
        plt.figure(figsize=(12, 8))
        
        for strategy_name, stats in valid_results.items():
            if stats and 'equity_curve' in stats:
                equity_curve = stats['equity_curve']
                if len(equity_curve) > 1:
                    plt.plot(equity_curve, label=strategy_name, linewidth=2)
        
        plt.axhline(y=investment, color='red', linestyle='--', alpha=0.7, label='初始投资')
        plt.title('策略资金曲线对比', fontsize=16)
        plt.xlabel('时间点')
        plt.ylabel('资金 (USDT)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('strategy_comparison.png', dpi=150, bbox_inches='tight')
        print(f"\n📈 资金曲线对比图已保存: strategy_comparison.png")
        
    except Exception as e:
        print(f"绘图失败: {e}")
    
    return results

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    try:
        results = run_strategy_comparison()
        print("\n策略对比分析完成！")
        
    except Exception as e:
        print(f"策略对比过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
