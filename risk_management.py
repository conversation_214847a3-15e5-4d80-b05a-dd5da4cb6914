"""
风险管理和资金管理模块
包含止损、止盈、仓位管理、风险控制等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass

@dataclass
class RiskMetrics:
    """风险指标"""
    max_drawdown: float
    var_95: float  # 95% VaR
    var_99: float  # 99% VaR
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_consecutive_losses: int
    current_drawdown: float
    risk_adjusted_return: float

class RiskManager:
    """风险管理器"""
    
    def __init__(self, 
                 max_portfolio_risk: float = 0.02,  # 单日最大风险2%
                 max_position_risk: float = 0.005,  # 单仓位最大风险0.5%
                 max_correlation: float = 0.7,      # 最大相关性
                 max_drawdown_limit: float = 0.15,  # 最大回撤限制15%
                 daily_loss_limit: float = 0.05,    # 日损失限制5%
                 margin_call_threshold: float = 0.8): # 保证金预警阈值
        
        self.max_portfolio_risk = max_portfolio_risk
        self.max_position_risk = max_position_risk
        self.max_correlation = max_correlation
        self.max_drawdown_limit = max_drawdown_limit
        self.daily_loss_limit = daily_loss_limit
        self.margin_call_threshold = margin_call_threshold
        
        self.daily_pnl_history = []
        self.equity_history = []
        self.position_correlations = {}
        
        self.logger = logging.getLogger(__name__)
    
    def calculate_position_risk(self, entry_price: float, current_price: float, 
                              quantity: float, leverage: int, side: str) -> float:
        """计算单个仓位的风险"""
        if side == 'long':
            unrealized_pnl = (current_price - entry_price) * quantity
        else:  # short
            unrealized_pnl = (entry_price - current_price) * quantity
        
        position_value = entry_price * quantity
        margin_used = position_value / leverage
        
        # 风险 = 潜在损失 / 保证金
        potential_loss = abs(min(0, unrealized_pnl))
        risk_ratio = potential_loss / margin_used if margin_used > 0 else 0
        
        return risk_ratio
    
    def calculate_portfolio_risk(self, positions: Dict, current_prices: Dict[str, float]) -> float:
        """计算投资组合总风险"""
        total_risk = 0
        
        for symbol, position in positions.items():
            if symbol in current_prices:
                position_risk = self.calculate_position_risk(
                    position.entry_price, 
                    current_prices[symbol],
                    position.quantity,
                    position.leverage,
                    position.side
                )
                total_risk += position_risk
        
        return total_risk
    
    def check_margin_requirements(self, positions: Dict, current_capital: float) -> Dict:
        """检查保证金要求"""
        total_margin_used = sum(pos.margin_used for pos in positions.values())
        margin_ratio = total_margin_used / current_capital if current_capital > 0 else 1
        
        return {
            'total_margin_used': total_margin_used,
            'available_margin': current_capital - total_margin_used,
            'margin_ratio': margin_ratio,
            'margin_call': margin_ratio > self.margin_call_threshold,
            'liquidation_risk': margin_ratio > 0.95
        }
    
    def calculate_optimal_position_size(self, symbol: str, entry_price: float, 
                                      stop_loss_price: float, available_capital: float,
                                      leverage: int, risk_per_trade: float = 0.01) -> float:
        """计算最优仓位大小"""
        # 基于风险的仓位计算
        risk_amount = available_capital * risk_per_trade
        price_risk = abs(entry_price - stop_loss_price) / entry_price
        
        if price_risk == 0:
            return 0
        
        # 考虑杠杆的仓位大小
        position_value = risk_amount / price_risk
        quantity = position_value / entry_price
        
        # 检查保证金要求
        required_margin = position_value / leverage
        if required_margin > available_capital * 0.1:  # 不超过可用资金的10%
            quantity = (available_capital * 0.1 * leverage) / entry_price
        
        return quantity
    
    def should_reduce_position(self, positions: Dict, current_prices: Dict[str, float],
                             current_capital: float) -> List[str]:
        """判断是否需要减仓"""
        symbols_to_reduce = []
        
        # 检查单个仓位风险
        for symbol, position in positions.items():
            if symbol in current_prices:
                position_risk = self.calculate_position_risk(
                    position.entry_price,
                    current_prices[symbol],
                    position.quantity,
                    position.leverage,
                    position.side
                )
                
                if position_risk > self.max_position_risk:
                    symbols_to_reduce.append(symbol)
                    self.logger.warning(f"{symbol} 仓位风险过高: {position_risk:.3f}")
        
        # 检查投资组合风险
        portfolio_risk = self.calculate_portfolio_risk(positions, current_prices)
        if portfolio_risk > self.max_portfolio_risk:
            # 选择风险最高的仓位减仓
            position_risks = []
            for symbol, position in positions.items():
                if symbol in current_prices:
                    risk = self.calculate_position_risk(
                        position.entry_price,
                        current_prices[symbol],
                        position.quantity,
                        position.leverage,
                        position.side
                    )
                    position_risks.append((symbol, risk))
            
            # 按风险排序，选择风险最高的
            position_risks.sort(key=lambda x: x[1], reverse=True)
            if position_risks and position_risks[0][0] not in symbols_to_reduce:
                symbols_to_reduce.append(position_risks[0][0])
        
        return symbols_to_reduce
    
    def calculate_stop_loss_levels(self, entry_price: float, side: str, 
                                 atr: float, risk_multiplier: float = 2.0) -> Dict:
        """计算止损位"""
        if side == 'long':
            stop_loss = entry_price - (atr * risk_multiplier)
            take_profit = entry_price + (atr * risk_multiplier * 1.5)
        else:  # short
            stop_loss = entry_price + (atr * risk_multiplier)
            take_profit = entry_price - (atr * risk_multiplier * 1.5)
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'risk_reward_ratio': 1.5
        }
    
    def update_risk_metrics(self, current_equity: float, daily_returns: List[float]) -> RiskMetrics:
        """更新风险指标"""
        self.equity_history.append(current_equity)
        
        if len(self.equity_history) < 2:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        # 计算收益率
        if not daily_returns:
            returns = pd.Series(self.equity_history).pct_change().dropna()
        else:
            returns = pd.Series(daily_returns)
        
        if len(returns) == 0:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)
        
        # 最大回撤
        equity_series = pd.Series(self.equity_history)
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min()
        current_drawdown = drawdown.iloc[-1]
        
        # VaR计算
        var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0
        var_99 = np.percentile(returns, 1) if len(returns) > 0 else 0
        
        # 夏普比率
        excess_returns = returns - 0.0001  # 假设无风险利率为0.01%
        sharpe_ratio = excess_returns.mean() / excess_returns.std() if excess_returns.std() > 0 else 0
        
        # 索提诺比率
        negative_returns = returns[returns < 0]
        downside_std = negative_returns.std() if len(negative_returns) > 0 else 0.001
        sortino_ratio = excess_returns.mean() / downside_std if downside_std > 0 else 0
        
        # 卡尔玛比率
        annual_return = returns.mean() * 365 * 24  # 假设小时数据
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 连续亏损
        consecutive_losses = self._calculate_consecutive_losses(returns)
        
        # 风险调整收益
        risk_adjusted_return = annual_return / returns.std() if returns.std() > 0 else 0
        
        return RiskMetrics(
            max_drawdown=max_drawdown,
            var_95=var_95,
            var_99=var_99,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_consecutive_losses=consecutive_losses,
            current_drawdown=current_drawdown,
            risk_adjusted_return=risk_adjusted_return
        )
    
    def _calculate_consecutive_losses(self, returns: pd.Series) -> int:
        """计算最大连续亏损次数"""
        if len(returns) == 0:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for ret in returns:
            if ret < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def emergency_stop_check(self, current_equity: float, initial_capital: float,
                           risk_metrics: RiskMetrics) -> bool:
        """紧急停止检查"""
        # 检查最大回撤
        if abs(risk_metrics.current_drawdown) > self.max_drawdown_limit:
            self.logger.critical(f"触发最大回撤限制: {risk_metrics.current_drawdown:.2%}")
            return True
        
        # 检查日损失限制
        if len(self.equity_history) >= 24:  # 至少一天的数据
            daily_loss = (current_equity - self.equity_history[-24]) / self.equity_history[-24]
            if daily_loss < -self.daily_loss_limit:
                self.logger.critical(f"触发日损失限制: {daily_loss:.2%}")
                return True
        
        # 检查连续亏损
        if risk_metrics.max_consecutive_losses > 10:
            self.logger.critical(f"连续亏损过多: {risk_metrics.max_consecutive_losses}")
            return True
        
        return False
    
    def generate_risk_report(self, risk_metrics: RiskMetrics, 
                           positions: Dict, current_prices: Dict[str, float]) -> str:
        """生成风险报告"""
        report = "=== 风险管理报告 ===\n\n"
        
        report += f"最大回撤: {risk_metrics.max_drawdown:.2%}\n"
        report += f"当前回撤: {risk_metrics.current_drawdown:.2%}\n"
        report += f"95% VaR: {risk_metrics.var_95:.2%}\n"
        report += f"99% VaR: {risk_metrics.var_99:.2%}\n"
        report += f"夏普比率: {risk_metrics.sharpe_ratio:.3f}\n"
        report += f"索提诺比率: {risk_metrics.sortino_ratio:.3f}\n"
        report += f"卡尔玛比率: {risk_metrics.calmar_ratio:.3f}\n"
        report += f"最大连续亏损: {risk_metrics.max_consecutive_losses}\n"
        report += f"风险调整收益: {risk_metrics.risk_adjusted_return:.3f}\n\n"
        
        # 仓位风险分析
        report += "=== 仓位风险分析 ===\n"
        for symbol, position in positions.items():
            if symbol in current_prices:
                position_risk = self.calculate_position_risk(
                    position.entry_price,
                    current_prices[symbol],
                    position.quantity,
                    position.leverage,
                    position.side
                )
                report += f"{symbol}: 风险 {position_risk:.3f}, 加仓次数 {position.add_count}\n"
        
        return report
