# 马丁格尔量化交易策略

这是一个专为加密货币市场设计的马丁格尔量化交易策略系统，支持10倍杠杆、多币种交易、智能加仓和风险管理。

## 策略特点

- **10倍杠杆交易**: 最大化资金利用效率
- **5%本金开仓**: 合理的初始仓位控制
- **智能加仓机制**: 最多3次加仓，2%亏损触发
- **多币种支持**: 最多同时持仓3个币种
- **风险管理**: 15%止损，8%止盈
- **技术指标信号**: 集成RSI、MACD、布林带等多种指标

## 系统架构

```
├── martingale_strategy.py    # 核心策略逻辑
├── technical_indicators.py   # 技术指标和信号生成
├── data_fetcher.py          # 数据获取模块
├── symbol_analyzer.py       # 交易对分析和选择
├── risk_management.py       # 风险管理模块
├── performance_analyzer.py  # 性能分析和报告
├── main_backtest.py         # 主回测程序
├── demo_backtest.py         # 演示程序
└── requirements.txt         # 依赖包列表
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行演示

```bash
python demo_backtest.py
```

这将使用模拟数据运行一个完整的回测演示，展示策略的基本功能。

### 3. 运行真实数据回测

```bash
python main_backtest.py
```

这将：
- 自动选择最适合马丁格尔策略的加密货币对
- 获取一年的历史数据
- 运行完整回测
- 生成详细的性能报告和图表

### 4. 仅分析交易对

```bash
python main_backtest.py analyze
```

这将分析各个加密货币对的适合性，不运行回测。

## 策略参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| initial_capital | 10000 USDT | 初始资金 |
| leverage | 10 | 杠杆倍数 |
| position_size_pct | 5% | 开仓比例 |
| max_add_positions | 3 | 最大加仓次数 |
| max_positions | 3 | 最大持仓币种数 |
| add_position_threshold | 2% | 加仓触发阈值 |
| stop_loss_pct | 15% | 止损比例 |
| take_profit_pct | 8% | 止盈比例 |

## 推荐的加密货币对

系统会自动分析并选择最适合马丁格尔策略的交易对，主要考虑因素：

1. **流动性**: 日成交量 > 100万USDT
2. **波动性**: 日波动率在1.5%-6%之间
3. **稳定性**: 最大回撤 < 50%
4. **均值回归特性**: 具有一定的价格回归倾向

推荐的一级交易对：
- BTC/USDT (比特币)
- ETH/USDT (以太坊)
- BNB/USDT (币安币)

## 风险管理

系统内置多层风险管理机制：

1. **仓位风险控制**: 单仓位风险不超过0.5%
2. **投资组合风险**: 总风险不超过2%
3. **保证金管理**: 保证金使用率不超过80%
4. **止损止盈**: 自动执行止损和止盈
5. **紧急停止**: 触发最大回撤限制时自动停止交易

## 性能指标

系统会计算以下性能指标：

- **收益指标**: 总收益率、年化收益率
- **风险指标**: 最大回撤、VaR、波动率
- **风险调整收益**: 夏普比率、索提诺比率、卡尔玛比率
- **交易统计**: 胜率、盈亏比、平均持仓时间等

## 输出文件

运行回测后会生成以下文件：

- `backtest_report.txt`: 详细的文本报告
- `equity_curve.html`: 交互式资金曲线图
- `trade_analysis.html`: 交易分析图表
- `backtest_results.json`: 完整的回测数据
- `backtest.log`: 运行日志

## 注意事项

⚠️ **重要提醒**:

1. **这是一个回测系统**: 仅用于策略验证，不构成投资建议
2. **马丁格尔风险**: 该策略在极端市场条件下可能面临重大损失
3. **杠杆风险**: 10倍杠杆会放大收益和损失
4. **实盘差异**: 实际交易中存在滑点、手续费等额外成本
5. **市场风险**: 加密货币市场波动极大，请谨慎投资

## 自定义配置

您可以通过修改 `main_backtest.py` 中的 `strategy_params` 来调整策略参数：

```python
strategy_params = {
    'initial_capital': 20000,      # 修改初始资金
    'leverage': 5,                 # 降低杠杆
    'position_size_pct': 0.03,     # 减小开仓比例
    'max_positions': 5,            # 增加最大持仓数
    # ... 其他参数
}
```

## 技术支持

如果您在使用过程中遇到问题，请检查：

1. 网络连接是否正常（需要访问交易所API）
2. 依赖包是否正确安装
3. 日志文件中的错误信息

## 免责声明

本系统仅供学习和研究使用。加密货币交易存在重大风险，可能导致本金损失。使用本系统进行实际交易的任何损失，开发者概不负责。请在充分了解风险的前提下谨慎使用。

## 许可证

MIT License - 详见 LICENSE 文件
