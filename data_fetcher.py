"""
数据获取模块
使用CCXT库从多个交易所获取历史价格数据
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Optional, Tuple
import os
import pickle

class DataFetcher:
    """数据获取器"""
    
    def __init__(self, exchange_name: str = 'binance', cache_dir: str = 'data_cache'):
        """
        初始化数据获取器
        
        Args:
            exchange_name: 交易所名称
            cache_dir: 数据缓存目录
        """
        self.exchange_name = exchange_name
        self.cache_dir = cache_dir
        self.exchange = self._init_exchange()
        
        # 创建缓存目录
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _init_exchange(self):
        """初始化交易所连接"""
        try:
            if self.exchange_name.lower() == 'binance':
                exchange = ccxt.binance({
                    'rateLimit': 1200,
                    'enableRateLimit': True,
                    'sandbox': False,
                })
            elif self.exchange_name.lower() == 'okx':
                exchange = ccxt.okx({
                    'rateLimit': 1200,
                    'enableRateLimit': True,
                    'sandbox': False,
                })
            elif self.exchange_name.lower() == 'bybit':
                exchange = ccxt.bybit({
                    'rateLimit': 1200,
                    'enableRateLimit': True,
                    'sandbox': False,
                })
            else:
                exchange = ccxt.binance({
                    'rateLimit': 1200,
                    'enableRateLimit': True,
                    'sandbox': False,
                })
            
            self.logger.info(f"成功连接到 {self.exchange_name} 交易所")
            return exchange
            
        except Exception as e:
            self.logger.error(f"连接交易所失败: {e}")
            raise
    
    def get_available_symbols(self, quote_currency: str = 'USDT') -> List[str]:
        """获取可用的交易对"""
        try:
            markets = self.exchange.load_markets()
            symbols = [symbol for symbol in markets.keys() 
                      if symbol.endswith(f'/{quote_currency}') and markets[symbol]['active']]
            
            self.logger.info(f"找到 {len(symbols)} 个 {quote_currency} 交易对")
            return symbols
            
        except Exception as e:
            self.logger.error(f"获取交易对失败: {e}")
            return []
    
    def get_top_volume_symbols(self, quote_currency: str = 'USDT', 
                              top_n: int = 50) -> List[str]:
        """获取成交量最大的交易对"""
        try:
            tickers = self.exchange.fetch_tickers()
            
            # 过滤指定报价货币的交易对
            filtered_tickers = {symbol: ticker for symbol, ticker in tickers.items() 
                              if symbol.endswith(f'/{quote_currency}')}
            
            # 按24小时成交量排序
            sorted_symbols = sorted(filtered_tickers.items(), 
                                  key=lambda x: x[1]['quoteVolume'] or 0, 
                                  reverse=True)
            
            top_symbols = [symbol for symbol, _ in sorted_symbols[:top_n]]
            
            self.logger.info(f"获取到前 {len(top_symbols)} 个高成交量交易对")
            return top_symbols
            
        except Exception as e:
            self.logger.error(f"获取高成交量交易对失败: {e}")
            return []
    
    def fetch_ohlcv(self, symbol: str, timeframe: str = '1h', 
                   since: Optional[datetime] = None, 
                   limit: int = 1000) -> pd.DataFrame:
        """
        获取OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            since: 开始时间
            limit: 数据条数限制
        """
        try:
            # 检查缓存
            cache_file = os.path.join(self.cache_dir, f"{symbol.replace('/', '_')}_{timeframe}.pkl")
            
            if os.path.exists(cache_file):
                cached_data = pd.read_pickle(cache_file)
                if not cached_data.empty:
                    last_time = cached_data.index[-1]
                    if datetime.now() - last_time < timedelta(hours=1):
                        self.logger.info(f"使用缓存数据: {symbol}")
                        return cached_data
            
            # 获取新数据
            if since:
                since_timestamp = int(since.timestamp() * 1000)
            else:
                since_timestamp = None
            
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, since_timestamp, limit)
            
            if not ohlcv:
                self.logger.warning(f"未获取到数据: {symbol}")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # 缓存数据
            df.to_pickle(cache_file)
            
            self.logger.info(f"获取数据成功: {symbol}, 数据量: {len(df)}")
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def fetch_historical_data(self, symbol: str, timeframe: str = '1h', 
                            days: int = 365) -> pd.DataFrame:
        """
        获取指定天数的历史数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            days: 历史天数
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            all_data = []
            current_time = start_time
            
            while current_time < end_time:
                data = self.fetch_ohlcv(symbol, timeframe, current_time, 1000)
                
                if data.empty:
                    break
                
                all_data.append(data)
                
                # 更新时间
                if timeframe == '1m':
                    current_time += timedelta(minutes=1000)
                elif timeframe == '5m':
                    current_time += timedelta(minutes=5000)
                elif timeframe == '15m':
                    current_time += timedelta(minutes=15000)
                elif timeframe == '1h':
                    current_time += timedelta(hours=1000)
                elif timeframe == '4h':
                    current_time += timedelta(hours=4000)
                elif timeframe == '1d':
                    current_time += timedelta(days=1000)
                
                # 避免请求过于频繁
                time.sleep(0.1)
            
            if all_data:
                combined_data = pd.concat(all_data)
                combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
                combined_data.sort_index(inplace=True)
                
                # 过滤到指定时间范围
                combined_data = combined_data[combined_data.index >= start_time]
                combined_data = combined_data[combined_data.index <= end_time]
                
                self.logger.info(f"获取 {symbol} {days}天历史数据成功, 数据量: {len(combined_data)}")
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_multiple_symbols_data(self, symbols: List[str], 
                                 timeframe: str = '1h', 
                                 days: int = 365) -> Dict[str, pd.DataFrame]:
        """
        批量获取多个交易对的数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            days: 历史天数
        """
        data_dict = {}
        
        for i, symbol in enumerate(symbols):
            self.logger.info(f"获取数据进度: {i+1}/{len(symbols)} - {symbol}")
            
            data = self.fetch_historical_data(symbol, timeframe, days)
            
            if not data.empty:
                data_dict[symbol] = data
            
            # 避免请求过于频繁
            time.sleep(0.5)
        
        self.logger.info(f"批量获取数据完成, 成功获取 {len(data_dict)} 个交易对数据")
        return data_dict
    
    def get_recommended_symbols(self) -> List[str]:
        """获取推荐的适合马丁格尔策略的交易对"""
        # 这些是流动性好、波动适中的主流币种
        recommended = [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT',
            'SOL/USDT', 'DOT/USDT', 'AVAX/USDT', 'MATIC/USDT', 'LINK/USDT',
            'UNI/USDT', 'LTC/USDT', 'BCH/USDT', 'ATOM/USDT', 'FTM/USDT',
            'NEAR/USDT', 'ALGO/USDT', 'VET/USDT', 'ICP/USDT', 'THETA/USDT'
        ]
        
        # 验证交易对是否可用
        available_symbols = self.get_available_symbols()
        valid_symbols = [symbol for symbol in recommended if symbol in available_symbols]
        
        self.logger.info(f"推荐交易对: {valid_symbols}")
        return valid_symbols
    
    def analyze_symbol_suitability(self, symbol: str, data: pd.DataFrame) -> Dict:
        """分析交易对是否适合马丁格尔策略"""
        if data.empty:
            return {'suitable': False, 'reason': '无数据'}
        
        # 计算波动性指标
        returns = data['close'].pct_change().dropna()
        volatility = returns.std() * np.sqrt(24)  # 日化波动率
        
        # 计算平均成交量
        avg_volume = data['volume'].mean()
        
        # 计算价格趋势
        price_change = (data['close'].iloc[-1] - data['close'].iloc[0]) / data['close'].iloc[0]
        
        # 适合性评分
        suitability_score = 0
        reasons = []
        
        # 波动性评估 (理想范围: 0.02-0.08)
        if 0.02 <= volatility <= 0.08:
            suitability_score += 30
            reasons.append(f"波动性适中 ({volatility:.3f})")
        elif volatility < 0.02:
            reasons.append(f"波动性过低 ({volatility:.3f})")
        else:
            reasons.append(f"波动性过高 ({volatility:.3f})")
        
        # 成交量评估
        if avg_volume > 1000000:  # 100万USDT
            suitability_score += 25
            reasons.append("成交量充足")
        else:
            reasons.append("成交量不足")
        
        # 趋势评估
        if abs(price_change) < 0.5:  # 价格变化不超过50%
            suitability_score += 25
            reasons.append("价格相对稳定")
        else:
            reasons.append("价格波动过大")
        
        # 数据完整性
        if len(data) > 8000:  # 至少一年的小时数据
            suitability_score += 20
            reasons.append("数据充足")
        else:
            reasons.append("数据不足")
        
        return {
            'suitable': suitability_score >= 60,
            'score': suitability_score,
            'volatility': volatility,
            'avg_volume': avg_volume,
            'price_change': price_change,
            'reasons': reasons
        }
