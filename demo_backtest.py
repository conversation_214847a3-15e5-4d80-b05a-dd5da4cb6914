"""
马丁格尔策略演示脚本
使用模拟数据快速展示策略效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 导入策略模块
from martingale_strategy import MartingaleStrategy, BacktestEngine, SignalType
from technical_indicators import SignalGenerator
from performance_analyzer import PerformanceAnalyzer

def generate_mock_data(symbol: str, days: int = 365, volatility: float = 0.03) -> pd.DataFrame:
    """生成模拟的加密货币价格数据"""
    
    # 生成时间序列
    start_date = datetime.now() - timedelta(days=days)
    timestamps = pd.date_range(start=start_date, periods=days*24, freq='H')
    
    # 生成价格数据（几何布朗运动）
    np.random.seed(42)  # 确保结果可重复
    
    # 基础参数
    initial_price = 50000 if 'BTC' in symbol else 3000 if 'ETH' in symbol else 300
    drift = 0.0001  # 微小的上涨趋势
    
    # 生成收益率
    returns = np.random.normal(drift, volatility, len(timestamps))
    
    # 添加一些趋势和周期性
    trend = np.sin(np.arange(len(timestamps)) * 2 * np.pi / (24 * 7)) * 0.001  # 周周期
    returns += trend
    
    # 计算价格
    prices = [initial_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成OHLCV数据
    data = pd.DataFrame(index=timestamps)
    data['close'] = prices
    
    # 生成开高低价（简化处理）
    noise = np.random.normal(0, volatility * 0.3, len(data))
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data['close'] * (1 + np.abs(noise))
    data['low'] = data['close'] * (1 - np.abs(noise))
    
    # 确保OHLC逻辑正确
    data['high'] = np.maximum(data[['open', 'close']].max(axis=1), data['high'])
    data['low'] = np.minimum(data[['open', 'close']].min(axis=1), data['low'])
    
    # 生成成交量
    base_volume = 1000000 if 'BTC' in symbol else 500000
    volume_noise = np.random.lognormal(0, 0.5, len(data))
    data['volume'] = base_volume * volume_noise
    
    return data

class MockSignalGenerator:
    """模拟信号生成器"""
    
    def generate_combined_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成模拟交易信号"""
        signals = pd.Series(SignalType.HOLD.value, index=data.index)
        
        # 计算简单的技术指标
        sma_short = data['close'].rolling(12).mean()
        sma_long = data['close'].rolling(26).mean()
        rsi = self._calculate_simple_rsi(data['close'], 14)
        
        # 生成信号
        # 金叉且RSI不超买时做多
        golden_cross = (sma_short > sma_long) & (sma_short.shift(1) <= sma_long.shift(1))
        long_condition = golden_cross & (rsi < 70)
        signals[long_condition] = SignalType.LONG.value
        
        # 死叉且RSI不超卖时做空
        death_cross = (sma_short < sma_long) & (sma_short.shift(1) >= sma_long.shift(1))
        short_condition = death_cross & (rsi > 30)
        signals[short_condition] = SignalType.SHORT.value
        
        return signals
    
    def _calculate_simple_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算简单RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

def run_demo_backtest():
    """运行演示回测"""
    print("=" * 60)
    print("马丁格尔量化交易策略演示")
    print("=" * 60)
    
    # 策略参数
    strategy_params = {
        'initial_capital': 10000,
        'leverage': 10,
        'position_size_pct': 0.05,
        'max_add_positions': 3,
        'max_positions': 3,
        'add_position_threshold': 0.02,
        'stop_loss_pct': 0.15,
        'take_profit_pct': 0.08
    }
    
    print("策略参数:")
    for key, value in strategy_params.items():
        print(f"  {key}: {value}")
    print()
    
    # 生成模拟数据
    print("生成模拟数据...")
    symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    data_dict = {}
    
    for symbol in symbols:
        volatility = 0.03 if 'BTC' in symbol else 0.04 if 'ETH' in symbol else 0.05
        data_dict[symbol] = generate_mock_data(symbol, days=365, volatility=volatility)
        print(f"  {symbol}: {len(data_dict[symbol])} 条数据")
    
    # 初始化策略组件
    print("\n初始化策略组件...")
    strategy = MartingaleStrategy(**strategy_params)
    signal_generator = MockSignalGenerator()
    backtest_engine = BacktestEngine(strategy, signal_generator)
    
    # 运行回测
    print("运行回测...")
    start_time = datetime.now()
    
    performance_stats = backtest_engine.run_backtest(data_dict)
    
    end_time = datetime.now()
    print(f"回测耗时: {(end_time - start_time).total_seconds():.2f} 秒")
    
    # 分析结果
    print("\n计算性能指标...")
    performance_analyzer = PerformanceAnalyzer()
    
    detailed_metrics = performance_analyzer.calculate_performance_metrics(
        strategy.equity_curve,
        strategy.trades,
        strategy.timestamps,
        strategy_params['initial_capital']
    )
    
    # 输出结果
    print("\n" + "=" * 60)
    print("回测结果摘要")
    print("=" * 60)
    
    print(f"初始资金: {strategy_params['initial_capital']:,} USDT")
    print(f"最终资金: {strategy.current_capital:,.2f} USDT")
    print(f"总收益: {strategy.current_capital - strategy_params['initial_capital']:,.2f} USDT")
    print(f"总收益率: {detailed_metrics.get('total_return', 0):.2%}")
    print(f"年化收益率: {detailed_metrics.get('annual_return', 0):.2%}")
    print(f"最大回撤: {detailed_metrics.get('max_drawdown', 0):.2%}")
    print(f"年化波动率: {detailed_metrics.get('volatility', 0):.2%}")
    print(f"夏普比率: {detailed_metrics.get('sharpe_ratio', 0):.3f}")
    print(f"索提诺比率: {detailed_metrics.get('sortino_ratio', 0):.3f}")
    
    # 交易统计
    trade_stats = detailed_metrics.get('trade_statistics', {})
    if trade_stats:
        print(f"\n交易统计:")
        print(f"  总交易次数: {trade_stats.get('total_trades', 0)}")
        print(f"  盈利交易: {trade_stats.get('winning_trades', 0)}")
        print(f"  亏损交易: {trade_stats.get('losing_trades', 0)}")
        print(f"  胜率: {trade_stats.get('win_rate', 0):.2%}")
        print(f"  盈亏比: {trade_stats.get('profit_factor', 0):.2f}")
        print(f"  平均盈利: {trade_stats.get('avg_profit', 0):.2f} USDT")
        print(f"  平均亏损: {trade_stats.get('avg_loss', 0):.2f} USDT")
        print(f"  最大连续盈利: {trade_stats.get('max_consecutive_wins', 0)}")
        print(f"  最大连续亏损: {trade_stats.get('max_consecutive_losses', 0)}")
    
    # 持仓分析
    if strategy.positions:
        print(f"\n当前持仓:")
        for symbol, position in strategy.positions.items():
            print(f"  {symbol}: {position.side} {position.quantity:.4f} @ {position.entry_price:.2f}")
    
    print("\n" + "=" * 60)
    
    # 绘制简单的资金曲线
    if len(strategy.equity_curve) > 1:
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        plt.plot(strategy.timestamps[:len(strategy.equity_curve)], strategy.equity_curve)
        plt.axhline(y=strategy_params['initial_capital'], color='r', linestyle='--', alpha=0.7)
        plt.title('资金曲线')
        plt.xlabel('时间')
        plt.ylabel('资金 (USDT)')
        plt.grid(True, alpha=0.3)
        
        # 计算回撤
        equity_series = pd.Series(strategy.equity_curve)
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak * 100
        
        plt.subplot(1, 2, 2)
        plt.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        plt.plot(drawdown, color='red')
        plt.title('回撤曲线')
        plt.xlabel('时间点')
        plt.ylabel('回撤 (%)')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('demo_results.png', dpi=150, bbox_inches='tight')
        print("图表已保存到: demo_results.png")
    
    return {
        'strategy': strategy,
        'performance_metrics': detailed_metrics,
        'data_dict': data_dict
    }

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    try:
        results = run_demo_backtest()
        print("\n演示完成！")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
